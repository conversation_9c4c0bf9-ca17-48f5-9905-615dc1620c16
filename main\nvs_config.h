/*
 * NVS Configuration Module for ESP32-C3
 * 用于管理设备配置的NVS存储模块
 */

#ifndef NVS_CONFIG_H
#define NVS_CONFIG_H

#include <stdint.h>
#include <stdbool.h>
#include "esp_err.h"

#ifdef __cplusplus
extern "C" {
#endif

// 配置结构体
typedef struct {
    uint8_t magic;              // 魔数，用于验证配置有效性 (0xAA)
    char stassid[32];           // WiFi SSID
    char stapsw[64];            // WiFi 密码
    char sn[32];                // 设备序列号
    char mqpwd[64];             // MQTT密码
    char mqtt_broker[128];      // MQTT服务器地址
    uint16_t mqtt_port;         // MQTT端口
    char mqtt_username[32];     // MQTT用户名
    char mqtt_client_id[32];    // MQTT客户端ID
    uint32_t reboot;            // 重启计数器
    float a1;                   // 参数a1 (正转时间)
    float a2;                   // 参数a2 (反转时间)
    uint8_t power_save_mode;    // 省电模式 (0=关闭, 1=最小, 2=最大)
    uint16_t publish_interval;  // MQTT发布间隔(秒)
    uint8_t reserved[32];       // 保留字段，用于未来扩展
} device_config_t;

// MAC地址字符串长度
#define MAC_STR_LEN 13

// 全局变量声明
extern device_config_t config;
extern char macStr[MAC_STR_LEN];
extern char sn[32];
extern char mqpwd[64];
extern uint8_t config_flag;

// 函数声明
/**
 * @brief 初始化NVS配置模块
 * @return ESP_OK 成功, 其他值表示错误
 */
esp_err_t nvs_config_init(void);

/**
 * @brief 加载配置从NVS
 * @return ESP_OK 成功, 其他值表示错误
 */
esp_err_t load_config(void);

/**
 * @brief 保存配置到NVS
 * @return ESP_OK 成功, 其他值表示错误
 */
esp_err_t save_config(void);

/**
 * @brief 恢复出厂设置
 * @return ESP_OK 成功, 其他值表示错误
 */
esp_err_t restore_factory(void);

/**
 * @brief 获取MAC地址字符串
 * @param mac_str 输出的MAC地址字符串缓冲区
 * @param len 缓冲区长度
 * @return ESP_OK 成功, 其他值表示错误
 */
esp_err_t get_mac_string(char *mac_str, size_t len);

/**
 * @brief 设置WiFi配置
 * @param ssid WiFi SSID
 * @param password WiFi密码
 * @return ESP_OK 成功, 其他值表示错误
 */
esp_err_t set_wifi_config(const char *ssid, const char *password);

/**
 * @brief 设置MQTT配置
 * @param broker MQTT服务器地址
 * @param port MQTT端口
 * @param username MQTT用户名
 * @param password MQTT密码
 * @param client_id MQTT客户端ID
 * @return ESP_OK 成功, 其他值表示错误
 */
esp_err_t set_mqtt_config(const char *broker, uint16_t port, 
                         const char *username, const char *password, 
                         const char *client_id);

/**
 * @brief 设置设备参数
 * @param a1 参数a1
 * @param a2 参数a2
 * @return ESP_OK 成功, 其他值表示错误
 */
esp_err_t set_device_params(float a1, float a2);

/**
 * @brief 打印当前配置
 */
void print_config(void);

/**
 * @brief 检查配置是否有效
 * @return true 有效, false 无效
 */
bool is_config_valid(void);

#ifdef __cplusplus
}
#endif

#endif // NVS_CONFIG_H

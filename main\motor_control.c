/*
 * Motor Control Implementation
 * 电机控制实现 - 从Arduino移植到ESP-IDF
 */

#include "motor_control.h"
#include "nvs_config.h"
#include "esp_log.h"
#include "driver/gpio.h"
#include "driver/ledc.h"
#include "freertos/FreeRTOS.h"
#include "freertos/task.h"
#include <string.h>

static const char *TAG = "MOTOR_CONTROL";

// 全局电机状态
static motor_state_t motor_state = {0};

// 初始化电机控制
esp_err_t motor_control_init(void)
{
    ESP_LOGI(TAG, "初始化电机控制模块");
    
    // 配置电机控制引脚
    gpio_config_t io_conf = {
        .intr_type = GPIO_INTR_DISABLE,
        .mode = GPIO_MODE_OUTPUT,
        .pin_bit_mask = (1ULL << MOTOR_FORWARD_PIN) | (1ULL << MOTOR_REVERSE_PIN),
        .pull_down_en = 0,
        .pull_up_en = 0,
    };
    esp_err_t ret = gpio_config(&io_conf);
    if (ret != ESP_OK) {
        ESP_LOGE(TAG, "电机控制引脚配置失败");
        return ret;
    }
    
    // 初始化电机为停止状态
    gpio_set_level(MOTOR_FORWARD_PIN, 0);
    gpio_set_level(MOTOR_REVERSE_PIN, 0);
    
    // 配置按钮输入引脚
    gpio_config_t button_conf = {
        .intr_type = GPIO_INTR_NEGEDGE,  // 下降沿触发
        .mode = GPIO_MODE_INPUT,
        .pin_bit_mask = (1ULL << GPIO_INPUT_PIN),
        .pull_down_en = 0,
        .pull_up_en = 1,  // 启用内部上拉
    };
    ret = gpio_config(&button_conf);
    if (ret != ESP_OK) {
        ESP_LOGE(TAG, "按钮引脚配置失败");
        return ret;
    }
    
    // 安装GPIO中断服务
    ret = gpio_install_isr_service(0);
    if (ret != ESP_OK && ret != ESP_ERR_INVALID_STATE) {
        ESP_LOGE(TAG, "GPIO中断服务安装失败");
        return ret;
    }
    
    // 添加按钮中断处理
    ret = gpio_isr_handler_add(GPIO_INPUT_PIN, button_isr_handler, NULL);
    if (ret != ESP_OK) {
        ESP_LOGE(TAG, "按钮中断处理器添加失败");
        return ret;
    }
    
    // LED控制已移至独立的led_control模块
    
    // 初始化电机状态
    memset(&motor_state, 0, sizeof(motor_state));
    
    ESP_LOGI(TAG, "电机控制模块初始化完成");
    return ESP_OK;
}

// 电机正转控制
void motor_control_forward(float duration_seconds)
{
    ESP_LOGI(TAG, "电机正转控制开始，持续时间: %.2f秒", duration_seconds);
    
    // 停止当前运行的电机
    motor_control_stop();
    vTaskDelay(pdMS_TO_TICKS(50)); // 短暂延迟，确保电机停止
    
    // 设置运行参数
    motor_state.direction = true;
    motor_state.run_duration = (uint32_t)(duration_seconds * 1000);
    motor_state.start_time = xTaskGetTickCount() * portTICK_PERIOD_MS;
    motor_state.is_running = true;
    
    // 启动电机正转
    ESP_LOGI(TAG, "设置电机正转: FORWARD=HIGH, REVERSE=LOW");
    gpio_set_level(MOTOR_FORWARD_PIN, 1);
    gpio_set_level(MOTOR_REVERSE_PIN, 0);
    
    ESP_LOGI(TAG, "电机开始时间: %lu", motor_state.start_time);
    
    // 运行指定时间
    vTaskDelay(pdMS_TO_TICKS(motor_state.run_duration));
    
    // 停止电机
    motor_control_stop();
    
    // 复位逻辑：反向转动短时间
    vTaskDelay(pdMS_TO_TICKS(1000)); // 等待1秒
    motor_reset(false, 0.02f); // 反向复位0.02秒
}

// 电机反转控制
void motor_control_reverse(float duration_seconds)
{
    ESP_LOGI(TAG, "电机反转控制开始，持续时间: %.2f秒", duration_seconds);
    
    // 停止当前运行的电机
    motor_control_stop();
    vTaskDelay(pdMS_TO_TICKS(50)); // 短暂延迟，确保电机停止
    
    // 设置运行参数
    motor_state.direction = false;
    motor_state.run_duration = (uint32_t)(duration_seconds * 1000);
    motor_state.start_time = xTaskGetTickCount() * portTICK_PERIOD_MS;
    motor_state.is_running = true;
    
    // 启动电机反转
    ESP_LOGI(TAG, "设置电机反转: FORWARD=LOW, REVERSE=HIGH");
    gpio_set_level(MOTOR_FORWARD_PIN, 0);
    gpio_set_level(MOTOR_REVERSE_PIN, 1);
    
    ESP_LOGI(TAG, "电机开始时间: %lu", motor_state.start_time);
    
    // 运行指定时间
    vTaskDelay(pdMS_TO_TICKS(motor_state.run_duration));
    
    // 停止电机
    motor_control_stop();
    
    // 复位逻辑：反向转动短时间
    vTaskDelay(pdMS_TO_TICKS(1000)); // 等待1秒
    motor_reset(true, 0.02f); // 反向复位0.02秒
}

// 停止电机
void motor_control_stop(void)
{
    ESP_LOGI(TAG, "停止电机: FORWARD=LOW, REVERSE=LOW");
    gpio_set_level(MOTOR_FORWARD_PIN, 0);
    gpio_set_level(MOTOR_REVERSE_PIN, 0);
    motor_state.is_running = false;
    
    uint32_t current_time = xTaskGetTickCount() * portTICK_PERIOD_MS;
    ESP_LOGI(TAG, "电机结束时间: %lu", current_time);
}

// 电机复位
void motor_reset(bool reverse_direction, float duration_seconds)
{
    ESP_LOGI(TAG, "电机复位开始，方向: %s, 持续时间: %.2f秒", 
             reverse_direction ? "正转" : "反转", duration_seconds);
    
    // 根据方向设置电机
    if (reverse_direction) {
        ESP_LOGI(TAG, "复位电机正转: FORWARD=HIGH, REVERSE=LOW");
        gpio_set_level(MOTOR_FORWARD_PIN, 1);
        gpio_set_level(MOTOR_REVERSE_PIN, 0);
    } else {
        ESP_LOGI(TAG, "复位电机反转: FORWARD=LOW, REVERSE=HIGH");
        gpio_set_level(MOTOR_FORWARD_PIN, 0);
        gpio_set_level(MOTOR_REVERSE_PIN, 1);
    }
    
    uint32_t start_time = xTaskGetTickCount() * portTICK_PERIOD_MS;
    ESP_LOGI(TAG, "复位开始时间: %lu", start_time);
    
    // 运行复位时间
    vTaskDelay(pdMS_TO_TICKS((uint32_t)(duration_seconds * 1000)));
    
    // 停止电机
    ESP_LOGI(TAG, "复位结束，停止电机: FORWARD=LOW, REVERSE=LOW");
    gpio_set_level(MOTOR_FORWARD_PIN, 0);
    gpio_set_level(MOTOR_REVERSE_PIN, 0);
    
    uint32_t end_time = xTaskGetTickCount() * portTICK_PERIOD_MS;
    ESP_LOGI(TAG, "复位结束时间: %lu", end_time);
    ESP_LOGI(TAG, "电机复位完成");
}

// 电机启动测试
void motor_startup_test(void)
{
    ESP_LOGI(TAG, "🔧 开始电机启动测试...");

    // 等待2秒，确保系统稳定
    vTaskDelay(pdMS_TO_TICKS(2000));

    ESP_LOGI(TAG, "🔧 测试电机正转 1秒");
    ESP_LOGI(TAG, "设置电机正转: FORWARD=HIGH, REVERSE=LOW");

    // 设置电机正转
    gpio_set_level(MOTOR_FORWARD_PIN, 1);
    gpio_set_level(MOTOR_REVERSE_PIN, 0);

    uint32_t start_time = xTaskGetTickCount() * portTICK_PERIOD_MS;
    ESP_LOGI(TAG, "电机测试开始时间: %lu ms", start_time);

    // 运行1秒
    vTaskDelay(pdMS_TO_TICKS(10000));

    // 停止电机
    ESP_LOGI(TAG, "停止电机: FORWARD=LOW, REVERSE=LOW");
    gpio_set_level(MOTOR_FORWARD_PIN, 0);
    gpio_set_level(MOTOR_REVERSE_PIN, 0);

    uint32_t end_time = xTaskGetTickCount() * portTICK_PERIOD_MS;
    ESP_LOGI(TAG, "电机测试结束时间: %lu ms", end_time);
    ESP_LOGI(TAG, "电机测试持续时间: %lu ms", end_time - start_time);

    // 等待1秒后进行复位
    vTaskDelay(pdMS_TO_TICKS(1000));

    ESP_LOGI(TAG, "🔧 测试复位：反转 0.02秒");
    motor_reset(false, 10.0f); // 反向复位0.02秒

    ESP_LOGI(TAG, "✅ 电机启动测试完成！");
}

// 检查电机状态
void motor_check_status(void)
{
    if (motor_state.is_running) {
        uint32_t current_time = xTaskGetTickCount() * portTICK_PERIOD_MS;
        if (current_time - motor_state.start_time >= motor_state.run_duration) {
            // 时间到，停止电机
            motor_control_stop();
            ESP_LOGI(TAG, "电机运行时间到，自动停止");
        }
    }
}

// 按钮中断处理函数
void IRAM_ATTR button_isr_handler(void *arg)
{
    // 中断处理程序代码应尽量简短，只标记中断发生
    motor_state.switch_sign = 1;
}

// 检查按钮状态并处理
void button_check_state(void)
{
    if (motor_state.switch_sign == 1) {
        // 获取当前时间
        uint32_t current_time = xTaskGetTickCount() * portTICK_PERIOD_MS;
        
        // 去抖动检查
        if (current_time - motor_state.last_debounce_time > DEBOUNCE_DELAY) {
            motor_state.last_debounce_time = current_time;
            
            // 读取当前引脚状态确认按钮是否真的被按下
            int button_state = gpio_get_level(GPIO_INPUT_PIN);
            if (button_state == 0) { // 按钮真的被按下
                // 切换开关状态
                motor_state.switch_state = !motor_state.switch_state;
                
                ESP_LOGI(TAG, "按钮按下确认，开关状态切换为: %s", 
                         motor_state.switch_state ? "ON" : "OFF");
                
                // 根据新状态控制电机
                extern device_config_t config;
                if (motor_state.switch_state) {
                    // 开关状态为开，控制电机正转
                    float duration = config.a1;
                    if (duration <= 0.0f) {
                        duration = 0.11f; // 使用默认值
                        ESP_LOGI(TAG, "使用默认正转时间: 0.11秒");
                    }
                    if (duration > 0) {
                        ESP_LOGI(TAG, "控制电机正转，持续时间: %.2f秒", duration);
                        motor_control_forward(duration);
                    }
                } else {
                    // 开关状态为关，控制电机反转
                    float duration = config.a2;
                    if (duration <= 0.0f) {
                        duration = 0.16f; // 使用默认值
                        ESP_LOGI(TAG, "使用默认反转时间: 0.16秒");
                    }
                    if (duration > 0) {
                        ESP_LOGI(TAG, "控制电机反转，持续时间: %.2f秒", duration);
                        motor_control_reverse(duration);
                    }
                }
            }
        }
        
        // 重置标志位
        motor_state.switch_sign = 0;
    }
}

// 获取电机状态
motor_state_t* motor_get_state(void)
{
    return &motor_state;
}

// 电机控制任务
void motor_control_task(void *pvParameters)
{
    ESP_LOGI(TAG, "电机控制任务启动");
    
    while (1) {
        // 检查电机状态
        motor_check_status();
        
        // 检查按钮状态
        button_check_state();
        
        // 任务延迟 - 10ms，与LED任务同步
        vTaskDelay(pdMS_TO_TICKS(10));
    }
}

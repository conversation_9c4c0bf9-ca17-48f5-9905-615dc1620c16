/*
 * NVS Configuration Module Implementation
 * ESP32-C3 NVS配置模块实现
 */

#include "nvs_config.h"
#include <string.h>
#include <stdio.h>
#include "esp_log.h"
#include "esp_mac.h"
#include "nvs_flash.h"
#include "nvs.h"
#include "freertos/FreeRTOS.h"
#include "freertos/task.h"

static const char *TAG = "nvs_config";

// 全局变量定义
device_config_t config;
char macStr[MAC_STR_LEN];
char sn[32];
char mqpwd[64];
uint8_t config_flag = 0;

// NVS命名空间
#define NVS_NAMESPACE_CONFIG "config"
#define NVS_NAMESPACE_FACTORY "facconfig"
#define NVS_KEY_CONFIG "config"
#define NVS_KEY_SN "sn"
#define NVS_KEY_MQPWD "mqpwd"

// 默认配置值
#define DEFAULT_MAGIC 0xAA
#define DEFAULT_A1 0.11f
#define DEFAULT_A2 0.16f
#define DEFAULT_MQTT_PORT 1883
#define DEFAULT_PUBLISH_INTERVAL 5
#define MAX_REBOOT_COUNT 4

/**
 * @brief 初始化NVS配置模块
 */
esp_err_t nvs_config_init(void)
{
    esp_err_t ret = nvs_flash_init();
    if (ret == ESP_ERR_NVS_NO_FREE_PAGES || ret == ESP_ERR_NVS_NEW_VERSION_FOUND) {
        ESP_ERROR_CHECK(nvs_flash_erase());
        ret = nvs_flash_init();
    }
    ESP_ERROR_CHECK(ret);
    
    ESP_LOGI(TAG, "NVS initialized");
    return ret;
}

/**
 * @brief 获取MAC地址字符串
 */
esp_err_t get_mac_string(char *mac_str, size_t len)
{
    if (mac_str == NULL || len < MAC_STR_LEN) {
        return ESP_ERR_INVALID_ARG;
    }
    
    uint8_t mac[6];
    esp_err_t ret = esp_read_mac(mac, ESP_MAC_WIFI_STA);
    if (ret != ESP_OK) {
        ESP_LOGE(TAG, "Failed to read MAC address");
        return ret;
    }
    
    snprintf(mac_str, len, "%02X%02X%02X%02X%02X%02X",
             mac[0] & 0xFF, mac[1] & 0xFF, mac[2] & 0xFF,
             mac[3] & 0xFF, mac[4] & 0xFF, mac[5] & 0xFF);
    
    ESP_LOGI(TAG, "MAC Address: %s", mac_str);
    return ESP_OK;
}

/**
 * @brief 初始化默认配置
 */
static void init_default_config(void)
{
    memset(&config, 0, sizeof(config));
    config.magic = DEFAULT_MAGIC;
    strcpy(config.stassid, "");  // 使用您之前设置的WiFi SSID
    strcpy(config.stapsw, "");  // 使用您之前设置的WiFi密码
    strcpy(config.mqtt_broker, "mqtt://wechat.taianhengrun.com");
    config.mqtt_port = DEFAULT_MQTT_PORT;
    strcpy(config.mqtt_username, "");
    strcpy(config.mqtt_client_id, "esp32c3_client");
    config.reboot = 0;
    config.a1 = DEFAULT_A1;
    config.a2 = DEFAULT_A2;
    config.power_save_mode = 1; // 默认启用最小省电模式
    config.publish_interval = DEFAULT_PUBLISH_INTERVAL;
    
    ESP_LOGI(TAG, "Default configuration initialized");
}

/**
 * @brief 加载配置从NVS
 */
esp_err_t load_config(void)
{
    esp_err_t ret;
    nvs_handle_t nvs_handle_config, nvs_handle_factory;
    
    // 获取MAC地址
    get_mac_string(macStr, sizeof(macStr));
    
    // 打开配置命名空间
    ret = nvs_open(NVS_NAMESPACE_CONFIG, NVS_READWRITE, &nvs_handle_config);
    if (ret != ESP_OK) {
        ESP_LOGE(TAG, "Error opening NVS config handle: %s", esp_err_to_name(ret));
        return ret;
    }
    
    // 打开工厂配置命名空间
    ret = nvs_open(NVS_NAMESPACE_FACTORY, NVS_READWRITE, &nvs_handle_factory);
    if (ret != ESP_OK) {
        ESP_LOGE(TAG, "Error opening NVS factory handle: %s", esp_err_to_name(ret));
        nvs_close(nvs_handle_config);
        return ret;
    }
    
    // 读取主配置
    size_t required_size = sizeof(config);
    ret = nvs_get_blob(nvs_handle_config, NVS_KEY_CONFIG, &config, &required_size);
    if (ret == ESP_ERR_NVS_NOT_FOUND) {
        ESP_LOGW(TAG, "Config not found, initializing default config");
        init_default_config();
        config_flag = 1;
    } else if (ret != ESP_OK) {
        ESP_LOGE(TAG, "Error reading config from NVS: %s", esp_err_to_name(ret));
        nvs_close(nvs_handle_config);
        nvs_close(nvs_handle_factory);
        return ret;
    }
    
    // 读取序列号
    size_t sn_size = sizeof(sn);
    ret = nvs_get_blob(nvs_handle_factory, NVS_KEY_SN, sn, &sn_size);
    if (ret == ESP_ERR_NVS_NOT_FOUND) {
        ESP_LOGW(TAG, "SN not found, using MAC as SN");
        strcpy(sn, macStr);
    } else if (ret != ESP_OK) {
        ESP_LOGE(TAG, "Error reading SN from NVS: %s", esp_err_to_name(ret));
    }

    // 将SN复制到配置结构体中
    strncpy(config.sn, sn, sizeof(config.sn) - 1);
    config.sn[sizeof(config.sn) - 1] = '\0';
    ESP_LOGI(TAG, "✅ SN已设置到配置结构体: %s", config.sn);
    
    // 读取MQTT密码
    size_t mqpwd_size = sizeof(mqpwd);
    ret = nvs_get_blob(nvs_handle_factory, NVS_KEY_MQPWD, mqpwd, &mqpwd_size);
    if (ret == ESP_ERR_NVS_NOT_FOUND) {
        ESP_LOGW(TAG, "MQTT password not found");
        strcpy(mqpwd, "");
    } else if (ret != ESP_OK) {
        ESP_LOGE(TAG, "Error reading MQTT password from NVS: %s", esp_err_to_name(ret));
    }
    
    // 验证和修正配置
    if (config.magic != DEFAULT_MAGIC) {
        ESP_LOGW(TAG, "Invalid magic number, marking config as invalid");
        config_flag = 1;
    }

    // 检查WiFi SSID是否为空，如果为空则需要配网
    if (strlen(config.stassid) == 0) {
        ESP_LOGW(TAG, "WiFi SSID is empty, need to configure WiFi");
        printf("⚠️ WiFi SSID为空，需要进入配网模式\n");
        config_flag = 1;
    }

    if (config.a1 <= 0.0f) {
        config.a1 = DEFAULT_A1;
        ESP_LOGW(TAG, "Invalid a1, set to default: %.2f", DEFAULT_A1);
    }

    if (config.a2 <= 0.0f) {
        config.a2 = DEFAULT_A2;
        ESP_LOGW(TAG, "Invalid a2, set to default: %.2f", DEFAULT_A2);
    }
    
    // 检查重启计数
    config.reboot++;
    if (config.reboot >= MAX_REBOOT_COUNT) {
        ESP_LOGW(TAG, "Too many reboots (%lu), restoring factory settings", config.reboot);
        restore_factory();
    }
    
    // 保存更新的配置
    save_config();
    
    // 重置重启计数
    vTaskDelay(2000 / portTICK_PERIOD_MS);
    config.reboot = 0;
    save_config();
    
    nvs_close(nvs_handle_config);
    nvs_close(nvs_handle_factory);
    
    ESP_LOGI(TAG, "Configuration loaded successfully");
    return ESP_OK;
}

/**
 * @brief 保存配置到NVS
 */
esp_err_t save_config(void)
{
    esp_err_t ret;
    nvs_handle_t nvs_handle;

    ret = nvs_open(NVS_NAMESPACE_CONFIG, NVS_READWRITE, &nvs_handle);
    if (ret != ESP_OK) {
        ESP_LOGE(TAG, "Error opening NVS handle for save: %s", esp_err_to_name(ret));
        return ret;
    }

    ret = nvs_set_blob(nvs_handle, NVS_KEY_CONFIG, &config, sizeof(config));
    if (ret != ESP_OK) {
        ESP_LOGE(TAG, "Error writing config to NVS: %s", esp_err_to_name(ret));
        nvs_close(nvs_handle);
        return ret;
    }

    ret = nvs_commit(nvs_handle);
    if (ret != ESP_OK) {
        ESP_LOGE(TAG, "Error committing to NVS: %s", esp_err_to_name(ret));
    }

    nvs_close(nvs_handle);
    ESP_LOGI(TAG, "Configuration saved successfully");
    return ret;
}

/**
 * @brief 恢复出厂设置 (保留SN和MQTT密码)
 */
esp_err_t restore_factory(void)
{
    ESP_LOGW(TAG, "Restoring factory settings (preserving SN and MQTT password)...");

    // 备份出厂参数
    char backup_sn[40];
    char backup_mqpwd[64];

    // 保存当前的SN和MQTT密码
    strncpy(backup_sn, sn, sizeof(backup_sn));
    strncpy(backup_mqpwd, mqpwd, sizeof(backup_mqpwd));

    ESP_LOGI(TAG, "备份出厂参数: SN=%s, MQTT密码=%s",
             backup_sn, strlen(backup_mqpwd) > 0 ? "已设置" : "未设置");

    // 只擦除用户配置命名空间，保留出厂配置
    nvs_handle_t nvs_handle_config;
    esp_err_t ret = nvs_open(NVS_NAMESPACE_CONFIG, NVS_READWRITE, &nvs_handle_config);
    if (ret == ESP_OK) {
        // 擦除用户配置命名空间中的所有键
        nvs_erase_all(nvs_handle_config);
        nvs_commit(nvs_handle_config);
        nvs_close(nvs_handle_config);
        ESP_LOGI(TAG, "用户配置已清除");
    } else {
        ESP_LOGW(TAG, "无法打开配置命名空间: %s", esp_err_to_name(ret));
    }

    // 初始化默认配置
    init_default_config();
    config_flag = 1;

    // 恢复出厂参数
    strncpy(sn, backup_sn, sizeof(sn));
    strncpy(mqpwd, backup_mqpwd, sizeof(mqpwd));

    ESP_LOGI(TAG, "恢复出厂参数: SN=%s, MQTT密码=%s",
             sn, strlen(mqpwd) > 0 ? "已恢复" : "未设置");

    // 保存默认配置
    save_config();

    ESP_LOGI(TAG, "Factory settings restored (SN and MQTT password preserved)");
    return ESP_OK;
}

/**
 * @brief 设置WiFi配置
 */
esp_err_t set_wifi_config(const char *ssid, const char *password)
{
    if (ssid == NULL || password == NULL) {
        return ESP_ERR_INVALID_ARG;
    }

    strncpy(config.stassid, ssid, sizeof(config.stassid) - 1);
    config.stassid[sizeof(config.stassid) - 1] = '\0';

    strncpy(config.stapsw, password, sizeof(config.stapsw) - 1);
    config.stapsw[sizeof(config.stapsw) - 1] = '\0';

    ESP_LOGI(TAG, "WiFi config updated: SSID=%s", ssid);
    return save_config();
}

/**
 * @brief 设置MQTT配置
 */
esp_err_t set_mqtt_config(const char *broker, uint16_t port,
                         const char *username, const char *password,
                         const char *client_id)
{
    if (broker == NULL) {
        return ESP_ERR_INVALID_ARG;
    }

    strncpy(config.mqtt_broker, broker, sizeof(config.mqtt_broker) - 1);
    config.mqtt_broker[sizeof(config.mqtt_broker) - 1] = '\0';

    config.mqtt_port = port;

    if (username != NULL) {
        strncpy(config.mqtt_username, username, sizeof(config.mqtt_username) - 1);
        config.mqtt_username[sizeof(config.mqtt_username) - 1] = '\0';
    }

    if (password != NULL) {
        strncpy(config.mqpwd, password, sizeof(config.mqpwd) - 1);
        config.mqpwd[sizeof(config.mqpwd) - 1] = '\0';
    }

    if (client_id != NULL) {
        strncpy(config.mqtt_client_id, client_id, sizeof(config.mqtt_client_id) - 1);
        config.mqtt_client_id[sizeof(config.mqtt_client_id) - 1] = '\0';
    }

    ESP_LOGI(TAG, "MQTT config updated: broker=%s, port=%d", broker, port);
    return save_config();
}

/**
 * @brief 设置设备参数
 */
esp_err_t set_device_params(float a1, float a2)
{
    if (a1 <= 0.0f || a2 <= 0.0f) {
        return ESP_ERR_INVALID_ARG;
    }

    config.a1 = a1;
    config.a2 = a2;

    ESP_LOGI(TAG, "Device params updated: a1=%.2f, a2=%.2f", a1, a2);
    return save_config();
}

/**
 * @brief 检查配置是否有效
 */
bool is_config_valid(void)
{
    return (config.magic == DEFAULT_MAGIC && config_flag == 0);
}

/**
 * @brief 打印当前配置
 */
void print_config(void)
{
    printf("=== Device Configuration ===\n");
    printf("MAC Address: %s\n", macStr);
    printf("Serial Number: %s\n", sn);
    printf("Magic: 0x%02X (Valid: %s)\n", config.magic, is_config_valid() ? "Yes" : "No");
    printf("Reboot Count: %lu\n", config.reboot);
    printf("\n--- WiFi Settings ---\n");
    printf("SSID: %s\n", config.stassid);
    printf("Password: %s\n", strlen(config.stapsw) > 0 ? "***" : "(empty)");
    printf("\n--- MQTT Settings ---\n");
    printf("Broker: %s\n", config.mqtt_broker);
    printf("Port: %d\n", config.mqtt_port);
    printf("Username: %s\n", config.mqtt_username);
    printf("Password: %s\n", strlen(config.mqpwd) > 0 ? "***" : "(empty)");
    printf("Client ID: %s\n", config.mqtt_client_id);
    printf("Publish Interval: %d seconds\n", config.publish_interval);
    printf("\n--- Device Parameters ---\n");
    printf("A1 (Forward Time): %.2f seconds\n", config.a1);
    printf("A2 (Reverse Time): %.2f seconds\n", config.a2);
    printf("Power Save Mode: %d\n", config.power_save_mode);
    printf("Config Flag: %d\n", config_flag);
    printf("============================\n");
}

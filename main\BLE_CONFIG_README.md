# ESP32-C3 蓝牙WiFi配网系统

这是一个完整的ESP32-C3蓝牙WiFi配网解决方案，支持通过手机APP进行WiFi配网。

## 功能特性

### 🔵 蓝牙配网
- **BLE广播**：设备名称 "ESP32C3-WiFiConfig"
- **自动超时**：5分钟配网超时保护
- **状态反馈**：实时向手机APP反馈连接状态
- **配置持久化**：WiFi配置自动保存到NVS

### 📱 手机APP交互
- **服务UUID**：0x1234
- **SSID特征**：0x1235 (写入WiFi名称)
- **密码特征**：0x1236 (写入WiFi密码)
- **状态特征**：0x1237 (读取连接状态)
- **设备信息**：0x1238 (读取设备信息)

### 🔄 智能配网流程
1. **检查配置**：启动时检查是否已有WiFi配置
2. **自动连接**：有配置则直接连接WiFi
3. **配网模式**：无配置或连接失败则进入蓝牙配网
4. **状态反馈**：实时向APP反馈配网进度

## 文件结构

```
main/
├── ble_wifi_config.h      # 蓝牙配网模块头文件
├── ble_wifi_config.c      # 蓝牙配网模块实现
├── wifi_manager.h         # WiFi管理器头文件
├── wifi_manager.c         # WiFi管理器实现
├── nvs_config.h           # NVS配置模块
├── nvs_config.c           # NVS配置实现
└── main_power_save.c      # 主程序
```

## 使用方法

### 1. 基本集成

```c
#include "wifi_manager.h"
#include "nvs_config.h"

void app_main(void)
{
    // 初始化NVS配置
    nvs_config_init();
    load_config();
    
    // 执行WiFi配网逻辑
    wait_key();
    
    // 检查配网结果
    if (is_wifi_configured()) {
        printf("WiFi配网成功！\n");
        // 继续其他初始化...
    }
}
```

### 2. 配网状态检查

```c
// 检查当前WiFi状态
wifi_state_t state = get_wifi_state();
switch (state) {
    case STATE_ONLINE:
        printf("WiFi已连接\n");
        break;
    case STATE_CONFIG:
        printf("正在配网中\n");
        break;
    case STATE_OFFLINE:
        printf("WiFi未连接\n");
        break;
}
```

## 配网流程详解

### 启动流程
```
1. 系统启动
2. 加载NVS配置
3. 检查config_flag
   ├─ config_flag = 0 → 直接连接WiFi
   └─ config_flag = 1 → 进入蓝牙配网模式
```

### 蓝牙配网流程
```
1. 启动BLE广播 (设备名: ESP32C3-WiFiConfig)
2. 等待手机APP连接
3. 接收WiFi SSID和密码
4. 尝试连接WiFi
5. 反馈连接结果给APP
6. 保存配置到NVS
7. 停止蓝牙配网
```

### 手机APP交互协议

#### 写入WiFi SSID
```
特征UUID: 0x1235
数据格式: 字符串 (最大31字节)
示例: "MyWiFiNetwork"
```

#### 写入WiFi密码
```
特征UUID: 0x1236
数据格式: 字符串 (最大63字节)
示例: "MyPassword123"
```

#### 读取连接状态
```
特征UUID: 0x1237
返回格式: 
- "CONNECTED:*************" (连接成功+IP地址)
- "FAILED" (连接失败)
```

## 配置参数

### 超时设置
```c
#define BLE_CONFIG_TIMEOUT_MS (5 * 60 * 1000)  // 5分钟
```

### 设备信息
```c
#define BLE_DEVICE_NAME "ESP32C3-WiFiConfig"
#define BLE_MANUFACTURER_DATA "Espressif"
```

### WiFi省电模式
```c
#define DEFAULT_PS_MODE WIFI_PS_MIN_MODEM
#define DEFAULT_LISTEN_INTERVAL 3
#define DEFAULT_BEACON_TIMEOUT 6
```

## 状态指示

### 串口输出示例
```
config_flag: 1
正在进入配网模式...
========================================
🔵 蓝牙配网已启动
📱 请使用手机APP进行WiFi配网
⏰ 配网将在5分钟后自动超时
⏱️  开始时间: 12345 ms
📡 设备名称: ESP32C3-WiFiConfig
========================================
📱 手机APP已连接
📥 收到WiFi配置:
   SSID: MyWiFi
   密码: ***
🔄 正在连接WiFi...
✅ WiFi连接成功！IP: *************
🎉 蓝牙配网成功！
🔴 蓝牙配网已停止
```

## 错误处理

### 常见问题
1. **蓝牙初始化失败**：检查蓝牙组件是否正确配置
2. **配网超时**：检查手机APP是否正确连接
3. **WiFi连接失败**：检查SSID和密码是否正确
4. **NVS存储失败**：检查NVS分区配置

### 调试方法
```c
// 打印详细状态
print_config();
print_wifi_manager_status();
ble_config_print_status();
```

## 手机APP开发建议

### BLE扫描
```javascript
// 扫描特定服务UUID的设备
const serviceUUID = "1234";
// 设备名称过滤
const deviceName = "ESP32C3-WiFiConfig";
```

### 配网步骤
1. 扫描并连接到ESP32C3设备
2. 发现服务和特征
3. 写入WiFi SSID到特征0x1235
4. 写入WiFi密码到特征0x1236
5. 监听状态特征0x1237的通知
6. 显示配网结果给用户

## 注意事项

1. **内存使用**：蓝牙栈会占用较多内存，注意堆栈大小配置
2. **功耗考虑**：配网完成后及时关闭蓝牙以节省功耗
3. **安全性**：WiFi密码在BLE传输过程中未加密，建议在生产环境中添加加密
4. **兼容性**：确保目标手机支持BLE 4.0+

这个蓝牙配网系统提供了完整的WiFi配网解决方案，可以直接集成到您的ESP32-C3项目中！

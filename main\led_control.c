/*
 * LED Control Implementation
 * LED控制实现 - 独立的LED状态管理
 */

#include "led_control.h"
#include "driver/gpio.h"
#include "esp_log.h"
#include "freertos/FreeRTOS.h"
#include "freertos/task.h"

static const char *TAG = "LED_CONTROL";

// 全局LED控制结构体
static led_control_t led_control = {
    .current_state = LED_STATE_OFF,
    .led_physical_state = false,
    .last_toggle_time = 0,
    .blink_interval = 0,
    .task_running = false
};

// 初始化LED控制
esp_err_t led_control_init(void)
{
    ESP_LOGI(TAG, "💡 初始化LED控制模块...");
    
    // 配置LED引脚为输出
    gpio_config_t led_config = {
        .pin_bit_mask = (1ULL << LED_PIN),
        .mode = GPIO_MODE_OUTPUT,
        .pull_up_en = GPIO_PULLUP_DISABLE,
        .pull_down_en = GPIO_PULLDOWN_DISABLE,
        .intr_type = GPIO_INTR_DISABLE
    };
    
    esp_err_t ret = gpio_config(&led_config);
    if (ret != ESP_OK) {
        ESP_LOGE(TAG, "LED引脚配置失败: %s", esp_err_to_name(ret));
        return ret;
    }
    
    // 初始化LED为熄灭状态
    gpio_set_level(LED_PIN, 0);
    led_control.led_physical_state = false;
    led_control.current_state = LED_STATE_OFF;
    
    ESP_LOGI(TAG, "✅ LED控制模块初始化完成 (GPIO%d)", LED_PIN);
    return ESP_OK;
}

// 设置LED物理状态
static void led_set_physical_state(bool state)
{
    gpio_set_level(LED_PIN, state ? 1 : 0);
    led_control.led_physical_state = state;
}

// 设置LED状态
esp_err_t led_set_state(led_state_t state)
{
    if (led_control.current_state == state) {
        return ESP_OK; // 状态相同，无需改变
    }
    
    led_control.current_state = state;
    led_control.last_toggle_time = xTaskGetTickCount() * portTICK_PERIOD_MS;
    
    switch (state) {
        case LED_STATE_OFF:
            ESP_LOGI(TAG, "💡 LED设置为: 熄灭");
            led_set_physical_state(false);
            led_control.blink_interval = 0;
            break;
            
        case LED_STATE_ON:
            ESP_LOGI(TAG, "💡 LED设置为: 常亮");
            led_set_physical_state(true);
            led_control.blink_interval = 0;
            break;
            
        case LED_STATE_FAST_BLINK:
            ESP_LOGI(TAG, "💡 LED设置为: 快闪 (配网模式)");
            led_control.blink_interval = LED_FAST_BLINK_INTERVAL_MS;
            led_set_physical_state(true); // 开始时点亮
            break;
            
        case LED_STATE_SLOW_BLINK:
            ESP_LOGI(TAG, "💡 LED设置为: 慢闪 (断网状态)");
            led_control.blink_interval = LED_SLOW_BLINK_INTERVAL_MS;
            led_set_physical_state(true); // 开始时点亮
            break;
            
        default:
            ESP_LOGW(TAG, "未知LED状态: %d", state);
            return ESP_ERR_INVALID_ARG;
    }
    
    return ESP_OK;
}

// 获取当前LED状态
led_state_t led_get_state(void)
{
    return led_control.current_state;
}

// LED控制任务
void led_control_task(void *pvParameters)
{
    ESP_LOGI(TAG, "💡 LED控制任务启动");
    led_control.task_running = true;
    
    while (led_control.task_running) {
        uint32_t current_time = xTaskGetTickCount() * portTICK_PERIOD_MS;
        
        // 处理闪烁状态
        if (led_control.current_state == LED_STATE_FAST_BLINK || 
            led_control.current_state == LED_STATE_SLOW_BLINK) {
            
            if (current_time - led_control.last_toggle_time >= led_control.blink_interval) {
                // 切换LED状态
                led_set_physical_state(!led_control.led_physical_state);
                led_control.last_toggle_time = current_time;
                
                ESP_LOGD(TAG, "LED闪烁切换: %s", 
                         led_control.led_physical_state ? "亮" : "灭");
            }
        }
        
        // 任务延迟
        vTaskDelay(pdMS_TO_TICKS(50)); // 50ms检查一次
    }
    
    ESP_LOGI(TAG, "💡 LED控制任务结束");
    vTaskDelete(NULL);
}

// 便捷函数实现
void led_set_on(void)
{
    led_set_state(LED_STATE_ON);
}

void led_set_off(void)
{
    led_set_state(LED_STATE_OFF);
}

void led_set_fast_blink(void)
{
    led_set_state(LED_STATE_FAST_BLINK);
}

void led_set_slow_blink(void)
{
    led_set_state(LED_STATE_SLOW_BLINK);
}

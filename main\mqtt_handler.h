/*
 * MQTT Handler Module for ESP32-C3 (Simplified)
 * MQTT消息处理模块 - 简化版本，只保留电机控制和配置更新
 */

#ifndef MQTT_HANDLER_H
#define MQTT_HANDLER_H

#include <stdint.h>
#include <stdbool.h>
#include "esp_err.h"
#include "cJSON.h"

// 消息类型定义
typedef enum {
    MSG_TYPE_MOTOR_CONTROL = 0,  // 电机控制
    MSG_TYPE_CONFIG_UPDATE = 3,  // 配置更新 (a1, a2)
    MSG_TYPE_FACTORY_RESET = 4   // 恢复出厂设置
} mqtt_message_type_t;

// 电机控制参数
typedef struct {
    bool direction;      // 电机方向 (0=正转, 1=反转)
    float duration;      // 运行时长(秒)
} motor_control_params_t;

// 配置更新参数
typedef struct {
    float angle1;        // 第一个角度值 (a1)
    float angle2;        // 第二个角度值 (a2)
} config_update_params_t;

// MQTT消息处理函数声明
void mqtt_handle_message(char *topic, uint8_t *payload, int length);
void mqtt_handle_motor_control(cJSON *params);
void mqtt_handle_config_update(cJSON *params);
void mqtt_handle_factory_reset(void);

#endif // MQTT_HANDLER_H

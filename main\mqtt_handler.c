/*
 * MQTT Handler Implementation (Simplified)
 * MQTT消息处理实现 - 简化版本，只处理电机控制和配置更新
 */

#include "mqtt_handler.h"
#include "motor_control.h"
#include "nvs_config.h"
#include "esp_log.h"
#include <string.h>
#include <stdio.h>

static const char *TAG = "MQTT_HANDLER";

// 处理MQTT消息 - 简化版本
void mqtt_handle_message(char *topic, uint8_t *payload, int length)
{
    // 创建以null结尾的字符串
    char *json_string = malloc(length + 1);
    if (json_string == NULL) {
        ESP_LOGE(TAG, "内存分配失败");
        return;
    }

    memcpy(json_string, payload, length);
    json_string[length] = '\0';

    ESP_LOGI(TAG, "处理JSON消息: %s", json_string);

    // 解析JSON
    cJSON *json = cJSON_Parse(json_string);
    if (json == NULL) {
        ESP_LOGE(TAG, "JSON解析失败");
        free(json_string);
        return;
    }

    // 获取参数对象
    cJSON *params = cJSON_GetObjectItem(json, "params");
    if (params == NULL) {
        ESP_LOGE(TAG, "未找到params字段");
        cJSON_Delete(json);
        free(json_string);
        return;
    }

    // 获取消息类型
    cJSON *type_item = cJSON_GetObjectItem(params, "type");
    int message_type = -1;

    if (type_item != NULL && cJSON_IsNumber(type_item)) {
        message_type = type_item->valueint;
        ESP_LOGI(TAG, "消息类型: %d", message_type);
    } else {
        // 如果没有type字段，尝试根据其他字段推断消息类型
        ESP_LOGW(TAG, "未找到type字段，尝试自动推断消息类型");

        cJSON *led_item = cJSON_GetObjectItem(params, "led");
        cJSON *angle_item = cJSON_GetObjectItem(params, "angle");
        cJSON *angle2_item = cJSON_GetObjectItem(params, "angle2");

        if (led_item != NULL && angle_item != NULL && angle2_item == NULL) {
            // 有led和angle字段，但没有angle2，推断为电机控制
            message_type = MSG_TYPE_MOTOR_CONTROL;
            ESP_LOGI(TAG, "推断为电机控制消息 (type=0)");
        } else if (angle_item != NULL && angle2_item != NULL) {
            // 有angle和angle2字段，推断为配置更新
            message_type = MSG_TYPE_CONFIG_UPDATE;
            ESP_LOGI(TAG, "推断为配置更新消息 (type=3)");
        } else {
            // 如果无法推断，默认设置为电机控制
            ESP_LOGW(TAG, "无法推断消息类型，默认设置为电机控制 (type=0)");
            message_type = MSG_TYPE_MOTOR_CONTROL;
        }
    }

    // 根据消息类型处理
    switch (message_type) {
        case MSG_TYPE_MOTOR_CONTROL:
            mqtt_handle_motor_control(params);
            break;

        case MSG_TYPE_CONFIG_UPDATE:
            mqtt_handle_config_update(params);
            break;

        case MSG_TYPE_FACTORY_RESET:
            mqtt_handle_factory_reset();
            break;

        default:
            ESP_LOGW(TAG, "未知消息类型: %d", message_type);
            break;
    }

    cJSON_Delete(json);
    free(json_string);
}

// 处理电机控制消息
void mqtt_handle_motor_control(cJSON *params)
{
    cJSON *led_item = cJSON_GetObjectItem(params, "led");
    cJSON *angle_item = cJSON_GetObjectItem(params, "angle");

    if (led_item == NULL || angle_item == NULL) {
        ESP_LOGE(TAG, "电机控制参数不完整");
        return;
    }

    bool direction = (led_item->valueint == 0); // 0为正转，1为反转
    float duration;

    // 处理angle字段 - 可能是字符串或数字
    if (cJSON_IsString(angle_item)) {
        duration = atof(angle_item->valuestring);
        ESP_LOGI(TAG, "解析duration字符串: '%s' -> %.3f", angle_item->valuestring, duration);
    } else if (cJSON_IsNumber(angle_item)) {
        duration = (float)angle_item->valuedouble;
        ESP_LOGI(TAG, "解析duration数字: %.3f", duration);
    } else {
        ESP_LOGE(TAG, "angle字段类型错误");
        return;
    }

    ESP_LOGI(TAG, "🔧 电机控制: 方向=%s, 持续时间=%.3f秒",
             direction ? "正转" : "反转", duration);

    // 调用电机控制函数
    if (direction) {
        motor_control_forward(duration);
    } else {
        motor_control_reverse(duration);
    }
}



// 处理配置更新消息
void mqtt_handle_config_update(cJSON *params)
{
    cJSON *angle1_item = cJSON_GetObjectItem(params, "angle");
    cJSON *angle2_item = cJSON_GetObjectItem(params, "angle2");

    if (angle1_item != NULL && angle2_item != NULL) {
        float angle1, angle2;

        // 处理angle字段 - 可能是字符串或数字
        if (cJSON_IsString(angle1_item)) {
            angle1 = atof(angle1_item->valuestring);
            ESP_LOGI(TAG, "解析angle字符串: '%s' -> %.3f", angle1_item->valuestring, angle1);
        } else if (cJSON_IsNumber(angle1_item)) {
            angle1 = (float)angle1_item->valuedouble;
            ESP_LOGI(TAG, "解析angle数字: %.3f", angle1);
        } else {
            ESP_LOGE(TAG, "angle字段类型错误");
            return;
        }

        // 处理angle2字段 - 可能是字符串或数字
        if (cJSON_IsString(angle2_item)) {
            angle2 = atof(angle2_item->valuestring);
            ESP_LOGI(TAG, "解析angle2字符串: '%s' -> %.3f", angle2_item->valuestring, angle2);
        } else if (cJSON_IsNumber(angle2_item)) {
            angle2 = (float)angle2_item->valuedouble;
            ESP_LOGI(TAG, "解析angle2数字: %.3f", angle2);
        } else {
            ESP_LOGE(TAG, "angle2字段类型错误");
            return;
        }

        // 更新配置 - 使用nvs_config的全局变量
        extern device_config_t config;
        config.a1 = angle1;
        config.a2 = angle2;
        config.magic = 0xAA;
        save_config();

        ESP_LOGI(TAG, "✅ 配置更新成功: a1=%.3f, a2=%.3f", angle1, angle2);
    } else {
        ESP_LOGE(TAG, "配置更新参数不完整 - 缺少angle或angle2字段");
    }
}

// 处理恢复出厂设置消息
void mqtt_handle_factory_reset(void)
{
    ESP_LOGW(TAG, "🔄 收到恢复出厂设置命令 (type=4)");
    ESP_LOGW(TAG, "⚠️ 即将恢复出厂设置，保留SN和MQTT密码");

    // 调用nvs_config中的恢复出厂设置函数
    esp_err_t ret = restore_factory();

    if (ret == ESP_OK) {
        ESP_LOGI(TAG, "✅ 恢复出厂设置成功 (SN和MQTT密码已保留)");
        ESP_LOGI(TAG, "🔄 设备将在2秒后重启...");

        // 延迟2秒后重启，让日志输出完成
        vTaskDelay(pdMS_TO_TICKS(2000));
        esp_restart();
    } else {
        ESP_LOGE(TAG, "❌ 恢复出厂设置失败: %s", esp_err_to_name(ret));
    }
}



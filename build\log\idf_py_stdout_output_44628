Command: cmake -G Ninja -DPYTHON_DEPS_CHECKED=1 -DPYTHON=C:\Users\<USER>\.espressif\python_env\idf5.5_py3.11_env\Scripts\python.exe -DESP_PLATFORM=1 -DSDKCONFIG='c:\Users\<USER>\wifi\sdkconfig' -DCCACHE_ENABLE=1 C:\Users\<USER>\wifi
-- Minimal build - OFF
-- ccache will be used for faster recompilation
-- git rev-parse returned 'fatal: not a git repository (or any of the parent directories): .git'
-- Could not use 'git describe' to determine PROJECT_VER.
-- Building ESP-IDF components for target esp32c3
-- Project sdkconfig file C:/Users/<USER>/wifi/sdkconfig
-- Adding linker script C:/Users/<USER>/esp/v5.5/esp-idf/components/riscv/ld/rom.api.ld
-- USING O3
-- App "wifi" version: 1
-- Adding linker script C:/Users/<USER>/wifi/build/esp-idf/esp_system/ld/memory.ld
-- Adding linker script C:/Users/<USER>/wifi/build/esp-idf/esp_system/ld/sections.ld.in
-- Adding linker script C:/Users/<USER>/esp/v5.5/esp-idf/components/esp_rom/esp32c3/ld/esp32c3.rom.ld
-- Adding linker script C:/Users/<USER>/esp/v5.5/esp-idf/components/esp_rom/esp32c3/ld/esp32c3.rom.api.ld
-- Adding linker script C:/Users/<USER>/esp/v5.5/esp-idf/components/esp_rom/esp32c3/ld/esp32c3.rom.bt_funcs.ld
-- Adding linker script C:/Users/<USER>/esp/v5.5/esp-idf/components/esp_rom/esp32c3/ld/esp32c3.rom.libgcc.ld
-- Adding linker script C:/Users/<USER>/esp/v5.5/esp-idf/components/esp_rom/esp32c3/ld/esp32c3.rom.version.ld
-- Adding linker script C:/Users/<USER>/esp/v5.5/esp-idf/components/esp_rom/esp32c3/ld/esp32c3.rom.ble_cca.ld
-- Adding linker script C:/Users/<USER>/esp/v5.5/esp-idf/components/esp_rom/esp32c3/ld/esp32c3.rom.ble_test.ld
-- Adding linker script C:/Users/<USER>/esp/v5.5/esp-idf/components/esp_rom/esp32c3/ld/esp32c3.rom.eco3.ld
-- Adding linker script C:/Users/<USER>/esp/v5.5/esp-idf/components/esp_rom/esp32c3/ld/esp32c3.rom.eco3_bt_funcs.ld
-- Adding linker script C:/Users/<USER>/esp/v5.5/esp-idf/components/esp_rom/esp32c3/ld/esp32c3.rom.libc.ld
-- Adding linker script C:/Users/<USER>/esp/v5.5/esp-idf/components/esp_rom/esp32c3/ld/esp32c3.rom.libc-suboptimal_for_misaligned_mem.ld
-- Adding linker script C:/Users/<USER>/esp/v5.5/esp-idf/components/esp_rom/esp32c3/ld/esp32c3.rom.newlib.ld
-- Adding linker script C:/Users/<USER>/esp/v5.5/esp-idf/components/soc/esp32c3/ld/esp32c3.peripherals.ld
-- Components: app_trace app_update bootloader bootloader_support bt cmock console cxx driver efuse esp-tls esp_adc esp_app_format esp_bootloader_format esp_coex esp_common esp_driver_ana_cmpr esp_driver_bitscrambler esp_driver_cam esp_driver_dac esp_driver_gpio esp_driver_gptimer esp_driver_i2c esp_driver_i2s esp_driver_isp esp_driver_jpeg esp_driver_ledc esp_driver_mcpwm esp_driver_parlio esp_driver_pcnt esp_driver_ppa esp_driver_rmt esp_driver_sdio esp_driver_sdm esp_driver_sdmmc esp_driver_sdspi esp_driver_spi esp_driver_touch_sens esp_driver_tsens esp_driver_twai esp_driver_uart esp_driver_usb_serial_jtag esp_eth esp_event esp_gdbstub esp_hid esp_http_client esp_http_server esp_https_ota esp_https_server esp_hw_support esp_lcd esp_local_ctrl esp_mm esp_netif esp_netif_stack esp_partition esp_phy esp_pm esp_psram esp_ringbuf esp_rom esp_security esp_system esp_timer esp_vfs_console esp_wifi espcoredump esptool_py fatfs freertos hal heap http_parser idf_test ieee802154 json log lwip main mbedtls mqtt newlib nvs_flash nvs_sec_provider openthread partition_table protobuf-c protocomm pthread riscv rt sdmmc soc spi_flash spiffs tcp_transport ulp unity usb vfs wear_levelling wifi_provisioning wpa_supplicant
-- Component paths: C:/Users/<USER>/esp/v5.5/esp-idf/components/app_trace C:/Users/<USER>/esp/v5.5/esp-idf/components/app_update C:/Users/<USER>/esp/v5.5/esp-idf/components/bootloader C:/Users/<USER>/esp/v5.5/esp-idf/components/bootloader_support C:/Users/<USER>/esp/v5.5/esp-idf/components/bt C:/Users/<USER>/esp/v5.5/esp-idf/components/cmock C:/Users/<USER>/esp/v5.5/esp-idf/components/console C:/Users/<USER>/esp/v5.5/esp-idf/components/cxx C:/Users/<USER>/esp/v5.5/esp-idf/components/driver C:/Users/<USER>/esp/v5.5/esp-idf/components/efuse C:/Users/<USER>/esp/v5.5/esp-idf/components/esp-tls C:/Users/<USER>/esp/v5.5/esp-idf/components/esp_adc C:/Users/<USER>/esp/v5.5/esp-idf/components/esp_app_format C:/Users/<USER>/esp/v5.5/esp-idf/components/esp_bootloader_format C:/Users/<USER>/esp/v5.5/esp-idf/components/esp_coex C:/Users/<USER>/esp/v5.5/esp-idf/components/esp_common C:/Users/<USER>/esp/v5.5/esp-idf/components/esp_driver_ana_cmpr C:/Users/<USER>/esp/v5.5/esp-idf/components/esp_driver_bitscrambler C:/Users/<USER>/esp/v5.5/esp-idf/components/esp_driver_cam C:/Users/<USER>/esp/v5.5/esp-idf/components/esp_driver_dac C:/Users/<USER>/esp/v5.5/esp-idf/components/esp_driver_gpio C:/Users/<USER>/esp/v5.5/esp-idf/components/esp_driver_gptimer C:/Users/<USER>/esp/v5.5/esp-idf/components/esp_driver_i2c C:/Users/<USER>/esp/v5.5/esp-idf/components/esp_driver_i2s C:/Users/<USER>/esp/v5.5/esp-idf/components/esp_driver_isp C:/Users/<USER>/esp/v5.5/esp-idf/components/esp_driver_jpeg C:/Users/<USER>/esp/v5.5/esp-idf/components/esp_driver_ledc C:/Users/<USER>/esp/v5.5/esp-idf/components/esp_driver_mcpwm C:/Users/<USER>/esp/v5.5/esp-idf/components/esp_driver_parlio C:/Users/<USER>/esp/v5.5/esp-idf/components/esp_driver_pcnt C:/Users/<USER>/esp/v5.5/esp-idf/components/esp_driver_ppa C:/Users/<USER>/esp/v5.5/esp-idf/components/esp_driver_rmt C:/Users/<USER>/esp/v5.5/esp-idf/components/esp_driver_sdio C:/Users/<USER>/esp/v5.5/esp-idf/components/esp_driver_sdm C:/Users/<USER>/esp/v5.5/esp-idf/components/esp_driver_sdmmc C:/Users/<USER>/esp/v5.5/esp-idf/components/esp_driver_sdspi C:/Users/<USER>/esp/v5.5/esp-idf/components/esp_driver_spi C:/Users/<USER>/esp/v5.5/esp-idf/components/esp_driver_touch_sens C:/Users/<USER>/esp/v5.5/esp-idf/components/esp_driver_tsens C:/Users/<USER>/esp/v5.5/esp-idf/components/esp_driver_twai C:/Users/<USER>/esp/v5.5/esp-idf/components/esp_driver_uart C:/Users/<USER>/esp/v5.5/esp-idf/components/esp_driver_usb_serial_jtag C:/Users/<USER>/esp/v5.5/esp-idf/components/esp_eth C:/Users/<USER>/esp/v5.5/esp-idf/components/esp_event C:/Users/<USER>/esp/v5.5/esp-idf/components/esp_gdbstub C:/Users/<USER>/esp/v5.5/esp-idf/components/esp_hid C:/Users/<USER>/esp/v5.5/esp-idf/components/esp_http_client C:/Users/<USER>/esp/v5.5/esp-idf/components/esp_http_server C:/Users/<USER>/esp/v5.5/esp-idf/components/esp_https_ota C:/Users/<USER>/esp/v5.5/esp-idf/components/esp_https_server C:/Users/<USER>/esp/v5.5/esp-idf/components/esp_hw_support C:/Users/<USER>/esp/v5.5/esp-idf/components/esp_lcd C:/Users/<USER>/esp/v5.5/esp-idf/components/esp_local_ctrl C:/Users/<USER>/esp/v5.5/esp-idf/components/esp_mm C:/Users/<USER>/esp/v5.5/esp-idf/components/esp_netif C:/Users/<USER>/esp/v5.5/esp-idf/components/esp_netif_stack C:/Users/<USER>/esp/v5.5/esp-idf/components/esp_partition C:/Users/<USER>/esp/v5.5/esp-idf/components/esp_phy C:/Users/<USER>/esp/v5.5/esp-idf/components/esp_pm C:/Users/<USER>/esp/v5.5/esp-idf/components/esp_psram C:/Users/<USER>/esp/v5.5/esp-idf/components/esp_ringbuf C:/Users/<USER>/esp/v5.5/esp-idf/components/esp_rom C:/Users/<USER>/esp/v5.5/esp-idf/components/esp_security C:/Users/<USER>/esp/v5.5/esp-idf/components/esp_system C:/Users/<USER>/esp/v5.5/esp-idf/components/esp_timer C:/Users/<USER>/esp/v5.5/esp-idf/components/esp_vfs_console C:/Users/<USER>/esp/v5.5/esp-idf/components/esp_wifi C:/Users/<USER>/esp/v5.5/esp-idf/components/espcoredump C:/Users/<USER>/esp/v5.5/esp-idf/components/esptool_py C:/Users/<USER>/esp/v5.5/esp-idf/components/fatfs C:/Users/<USER>/esp/v5.5/esp-idf/components/freertos C:/Users/<USER>/esp/v5.5/esp-idf/components/hal C:/Users/<USER>/esp/v5.5/esp-idf/components/heap C:/Users/<USER>/esp/v5.5/esp-idf/components/http_parser C:/Users/<USER>/esp/v5.5/esp-idf/components/idf_test C:/Users/<USER>/esp/v5.5/esp-idf/components/ieee802154 C:/Users/<USER>/esp/v5.5/esp-idf/components/json C:/Users/<USER>/esp/v5.5/esp-idf/components/log C:/Users/<USER>/esp/v5.5/esp-idf/components/lwip C:/Users/<USER>/wifi/main C:/Users/<USER>/esp/v5.5/esp-idf/components/mbedtls C:/Users/<USER>/esp/v5.5/esp-idf/components/mqtt C:/Users/<USER>/esp/v5.5/esp-idf/components/newlib C:/Users/<USER>/esp/v5.5/esp-idf/components/nvs_flash C:/Users/<USER>/esp/v5.5/esp-idf/components/nvs_sec_provider C:/Users/<USER>/esp/v5.5/esp-idf/components/openthread C:/Users/<USER>/esp/v5.5/esp-idf/components/partition_table C:/Users/<USER>/esp/v5.5/esp-idf/components/protobuf-c C:/Users/<USER>/esp/v5.5/esp-idf/components/protocomm C:/Users/<USER>/esp/v5.5/esp-idf/components/pthread C:/Users/<USER>/esp/v5.5/esp-idf/components/riscv C:/Users/<USER>/esp/v5.5/esp-idf/components/rt C:/Users/<USER>/esp/v5.5/esp-idf/components/sdmmc C:/Users/<USER>/esp/v5.5/esp-idf/components/soc C:/Users/<USER>/esp/v5.5/esp-idf/components/spi_flash C:/Users/<USER>/esp/v5.5/esp-idf/components/spiffs C:/Users/<USER>/esp/v5.5/esp-idf/components/tcp_transport C:/Users/<USER>/esp/v5.5/esp-idf/components/ulp C:/Users/<USER>/esp/v5.5/esp-idf/components/unity C:/Users/<USER>/esp/v5.5/esp-idf/components/usb C:/Users/<USER>/esp/v5.5/esp-idf/components/vfs C:/Users/<USER>/esp/v5.5/esp-idf/components/wear_levelling C:/Users/<USER>/esp/v5.5/esp-idf/components/wifi_provisioning C:/Users/<USER>/esp/v5.5/esp-idf/components/wpa_supplicant
-- Configuring done (24.3s)
-- Generating done (1.3s)
-- Build files have been written to: C:/Users/<USER>/wifi/build

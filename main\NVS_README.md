# ESP32-C3 NVS配置模块

这是一个独立的NVS（非易失性存储）配置管理模块，专为ESP32-C3设计，提供完整的设备配置存储和管理功能。

## 功能特性

### 🔧 配置管理
- **WiFi配置**：SSID和密码存储
- **MQTT配置**：服务器地址、端口、认证信息
- **设备参数**：自定义参数a1、a2（如正转/反转时间）
- **系统设置**：省电模式、发布间隔等

### 🛡️ 安全特性
- **配置验证**：魔数验证确保配置完整性
- **重启保护**：连续重启检测，自动恢复出厂设置
- **错误处理**：完整的错误处理和日志记录

### 💾 存储结构
- **主配置**：存储在`config`命名空间
- **工厂配置**：存储在`facconfig`命名空间
- **序列号**：独立存储，支持MAC地址作为默认值

## 文件结构

```
main/
├── nvs_config.h        # 头文件，包含所有接口定义
├── nvs_config.c        # 实现文件，包含所有功能实现
├── nvs_test.c          # 测试程序，演示模块使用
└── NVS_README.md       # 本文档
```

## 配置结构体

```c
typedef struct {
    uint8_t magic;              // 魔数验证 (0xAA)
    char stassid[32];           // WiFi SSID
    char stapsw[64];            // WiFi 密码
    char sn[32];                // 设备序列号
    char mqpwd[64];             // MQTT密码
    char mqtt_broker[128];      // MQTT服务器地址
    uint16_t mqtt_port;         // MQTT端口
    char mqtt_username[32];     // MQTT用户名
    char mqtt_client_id[32];    // MQTT客户端ID
    uint32_t reboot;            // 重启计数器
    float a1;                   // 参数a1
    float a2;                   // 参数a2
    uint8_t power_save_mode;    // 省电模式
    uint16_t publish_interval;  // 发布间隔
    uint8_t reserved[32];       // 保留字段
} device_config_t;
```

## API接口

### 初始化函数
```c
esp_err_t nvs_config_init(void);        // 初始化NVS系统
esp_err_t load_config(void);             // 加载配置
```

### 配置操作
```c
esp_err_t save_config(void);             // 保存配置
esp_err_t restore_factory(void);        // 恢复出厂设置
```

### 配置设置
```c
esp_err_t set_wifi_config(const char *ssid, const char *password);
esp_err_t set_mqtt_config(const char *broker, uint16_t port, 
                         const char *username, const char *password, 
                         const char *client_id);
esp_err_t set_device_params(float a1, float a2);
```

### 工具函数
```c
esp_err_t get_mac_string(char *mac_str, size_t len);
bool is_config_valid(void);
void print_config(void);
```

## 使用示例

### 基本使用流程

```c
#include "nvs_config.h"

void app_main(void)
{
    // 1. 初始化NVS
    nvs_config_init();
    
    // 2. 加载配置
    load_config();
    
    // 3. 打印当前配置
    print_config();
    
    // 4. 修改配置（可选）
    set_wifi_config("MyWiFi", "MyPassword");
    set_mqtt_config("mqtt://broker.example.com", 1883, "user", "pass", "client1");
    
    // 5. 使用配置
    printf("WiFi SSID: %s\n", config.stassid);
    printf("MQTT Broker: %s\n", config.mqtt_broker);
}
```

### 配置验证

```c
if (is_config_valid()) {
    // 配置有效，正常使用
    connect_wifi(config.stassid, config.stapsw);
} else {
    // 配置无效，进入配置模式
    start_config_mode();
}
```

## 默认配置

| 参数 | 默认值 | 说明 |
|------|--------|------|
| magic | 0xAA | 配置验证魔数 |
| stassid | "YOUR_WIFI_SSID" | WiFi SSID |
| stapsw | "YOUR_WIFI_PASSWORD" | WiFi密码 |
| mqtt_broker | "mqtt://broker.emqx.io" | MQTT服务器 |
| mqtt_port | 1883 | MQTT端口 |
| a1 | 0.11 | 参数a1（秒） |
| a2 | 0.16 | 参数a2（秒） |
| power_save_mode | 1 | 省电模式 |
| publish_interval | 5 | 发布间隔（秒） |

## 错误处理

模块提供完整的错误处理：

- **ESP_OK**：操作成功
- **ESP_ERR_INVALID_ARG**：参数无效
- **ESP_ERR_NVS_NOT_FOUND**：配置不存在
- **ESP_ERR_NO_MEM**：内存不足

## 安全特性

### 重启保护
- 连续重启超过4次自动恢复出厂设置
- 防止配置错误导致的启动循环

### 配置验证
- 魔数验证确保配置完整性
- 参数范围检查防止无效值

## 编译和测试

```bash
# 编译测试程序
idf.py build

# 烧录和监控
idf.py flash monitor
```

## 注意事项

1. **NVS分区**：确保NVS分区足够大（建议至少24KB）
2. **字符串长度**：注意各字符串字段的最大长度限制
3. **浮点精度**：a1、a2参数使用float类型，注意精度
4. **线程安全**：当前实现不是线程安全的，多线程使用需要加锁

## 扩展功能

可以轻松扩展更多配置项：

1. 在`device_config_t`结构体中添加新字段
2. 在`init_default_config()`中设置默认值
3. 添加对应的设置函数
4. 更新`print_config()`函数

这个模块提供了完整的配置管理解决方案，可以直接集成到您的ESP32-C3项目中！

{"C_Cpp.intelliSenseEngine": "default", "idf.espIdfPathWin": "C:\\Users\\<USER>\\esp\\v5.5\\esp-idf", "idf.pythonInstallPath": "C:\\Users\\<USER>\\.espressif\\tools\\idf-python\\3.11.2\\python.exe", "idf.openOcdConfigs": ["board/esp32c3-builtin.cfg"], "idf.toolsPathWin": "C:\\Users\\<USER>\\.espressif", "idf.customExtraVars": {"IDF_TARGET": "esp32c3"}, "clangd.path": "C:\\Users\\<USER>\\.espressif\\tools\\esp-clang\\esp-19.1.2_20250312\\esp-clang\\bin\\clangd.exe", "clangd.arguments": ["--background-index", "--query-driver=C:\\Users\\<USER>\\.espressif\\tools\\xtensa-esp-elf\\esp-14.2.0_20241119\\xtensa-esp-elf\\bin\\xtensa-esp32-elf-gcc.exe", "--compile-commands-dir=${workspaceFolder}\\build"], "idf.portWin": "COM8", "idf.flashType": "UART", "files.associations": {"esp_log.h": "c", "ostream": "cpp"}}
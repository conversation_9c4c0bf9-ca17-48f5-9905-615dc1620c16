/*
 * Battery Monitor Implementation
 * 电池监测实现 - 18650双电池监测
 */

#include "battery_monitor.h"
#include "esp_log.h"
#include "esp_adc/adc_oneshot.h"
#include "esp_adc/adc_cali.h"
#include "esp_adc/adc_cali_scheme.h"
#include "freertos/FreeRTOS.h"
#include "freertos/task.h"
#include "cJSON.h"
#include <string.h>
#include <math.h>

static const char *TAG = "BATTERY_MONITOR";

// 全局变量
static battery_status_t battery_status = {0};
static adc_oneshot_unit_handle_t adc1_handle = NULL;
static adc_cali_handle_t adc1_cali_handle = NULL;
static bool adc_calibrated = false;

// 初始化电池监测
esp_err_t battery_monitor_init(void)
{
    ESP_LOGI(TAG, "🔋 初始化电池监测模块...");
    
    // 配置ADC
    adc_oneshot_unit_init_cfg_t init_config1 = {
        .unit_id = ADC_UNIT_1,
    };
    ESP_ERROR_CHECK(adc_oneshot_new_unit(&init_config1, &adc1_handle));
    
    // 配置ADC通道
    adc_oneshot_chan_cfg_t config = {
        .bitwidth = BATTERY_ADC_BITWIDTH,
        .atten = BATTERY_ADC_ATTEN,
    };
    ESP_ERROR_CHECK(adc_oneshot_config_channel(adc1_handle, BATTERY_ADC_CHANNEL, &config));
    
    // ADC校准
    adc_cali_curve_fitting_config_t cali_config = {
        .unit_id = ADC_UNIT_1,
        .atten = BATTERY_ADC_ATTEN,
        .bitwidth = BATTERY_ADC_BITWIDTH,
    };
    esp_err_t ret = adc_cali_create_scheme_curve_fitting(&cali_config, &adc1_cali_handle);
    if (ret == ESP_OK) {
        adc_calibrated = true;
        ESP_LOGI(TAG, "✅ ADC校准成功");
    } else {
        ESP_LOGW(TAG, "⚠️ ADC校准失败，使用原始值");
    }
    
    // 初始化电池状态
    battery_status.voltage = 0.0f;
    battery_status.percentage = 0;
    battery_status.is_low = false;
    battery_status.is_charging = false;
    strcpy(battery_status.health_status, "unknown");
    battery_status.last_measure_time = 0;
    battery_status.first_measure_done = false;
    
    ESP_LOGI(TAG, "✅ 电池监测模块初始化完成");
    return ESP_OK;
}

// 读取电池电压
esp_err_t battery_read_voltage(float *voltage)
{
    if (voltage == NULL) {
        return ESP_ERR_INVALID_ARG;
    }
    
    int adc_raw = 0;
    int adc_voltage = 0;
    
    // 读取ADC原始值
    esp_err_t ret = adc_oneshot_read(adc1_handle, BATTERY_ADC_CHANNEL, &adc_raw);
    if (ret != ESP_OK) {
        ESP_LOGE(TAG, "ADC读取失败: %s", esp_err_to_name(ret));
        return ret;
    }
    
    // 转换为电压值
    if (adc_calibrated) {
        ret = adc_cali_raw_to_voltage(adc1_cali_handle, adc_raw, &adc_voltage);
        if (ret != ESP_OK) {
            ESP_LOGE(TAG, "ADC校准转换失败: %s", esp_err_to_name(ret));
            return ret;
        }
    } else {
        // 使用简单的线性转换
        adc_voltage = (adc_raw * 3300) / 4095; // 12位ADC，参考电压3.3V
    }
    
    // 计算实际电池电压 (考虑分压比)
    *voltage = (adc_voltage / 1000.0f) * VOLTAGE_DIVIDER_RATIO;
    
    ESP_LOGD(TAG, "ADC原始值: %d, ADC电压: %d mV, 电池电压: %.3f V", 
             adc_raw, adc_voltage, *voltage);
    
    return ESP_OK;
}

// 电压转换为电量百分比
int battery_voltage_to_percentage(float voltage)
{
    if (voltage <= BATTERY_MIN_VOLTAGE) {
        return 0;
    } else if (voltage >= BATTERY_MAX_VOLTAGE) {
        return 100;
    } else {
        // 线性插值计算百分比
        float percentage = ((voltage - BATTERY_MIN_VOLTAGE) / 
                           (BATTERY_MAX_VOLTAGE - BATTERY_MIN_VOLTAGE)) * 100.0f;
        return (int)roundf(percentage);
    }
}

// 获取健康状态
const char* battery_get_health_status(int percentage)
{
    if (percentage >= 75) {
        return "excellent";
    } else if (percentage >= 50) {
        return "good";
    } else if (percentage >= 25) {
        return "fair";
    } else if (percentage >= 10) {
        return "poor";
    } else {
        return "critical";
    }
}

// 更新电池状态
esp_err_t battery_update_status(void)
{
    esp_err_t ret = battery_read_voltage(&battery_status.voltage);
    if (ret != ESP_OK) {
        return ret;
    }
    
    battery_status.percentage = battery_voltage_to_percentage(battery_status.voltage);
    battery_status.is_low = (battery_status.percentage <= BATTERY_LOW_THRESHOLD);
    strcpy(battery_status.health_status, battery_get_health_status(battery_status.percentage));
    battery_status.last_measure_time = xTaskGetTickCount() * portTICK_PERIOD_MS;
    
    if (!battery_status.first_measure_done) {
        battery_status.first_measure_done = true;
        ESP_LOGI(TAG, "🔋 首次电池测量完成");
    }
    
    ESP_LOGI(TAG, "🔋 电池状态: %.3fV, %d%%, %s", 
             battery_status.voltage, battery_status.percentage, battery_status.health_status);
    
    return ESP_OK;
}

// 获取电池状态
battery_status_t* battery_get_status(void)
{
    return &battery_status;
}

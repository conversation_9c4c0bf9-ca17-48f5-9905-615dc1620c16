/*
 * LED Control Module for ESP32-C3
 * LED控制模块 - 独立的LED状态管理
 */

#ifndef LED_CONTROL_H
#define LED_CONTROL_H

#include <stdint.h>
#include <stdbool.h>
#include "esp_err.h"
#include "freertos/FreeRTOS.h"
#include "freertos/task.h"

// LED引脚定义
#define LED_PIN 8           // ESP32-C3 LED引脚

// LED状态定义
typedef enum {
    LED_STATE_OFF = 0,      // 熄灭
    LED_STATE_ON,           // 常亮
    LED_STATE_FAST_BLINK,   // 快闪 (配网模式)
    LED_STATE_SLOW_BLINK    // 慢闪 (断网状态)
} led_state_t;

// LED闪烁参数
#define LED_FAST_BLINK_INTERVAL_MS  200   // 快闪间隔 200ms
#define LED_SLOW_BLINK_INTERVAL_MS  1000  // 慢闪间隔 1000ms

// LED控制结构体
typedef struct {
    led_state_t current_state;      // 当前LED状态
    bool led_physical_state;        // LED物理状态 (true=亮, false=灭)
    uint32_t last_toggle_time;      // 上次切换时间
    uint32_t blink_interval;        // 当前闪烁间隔
    bool task_running;              // 任务运行状态
} led_control_t;

// LED控制函数声明
esp_err_t led_control_init(void);
esp_err_t led_set_state(led_state_t state);
led_state_t led_get_state(void);
void led_control_task(void *pvParameters);

// 便捷的状态设置函数
void led_set_on(void);          // 设置常亮
void led_set_off(void);         // 设置熄灭
void led_set_fast_blink(void);  // 设置快闪 (配网模式)
void led_set_slow_blink(void);  // 设置慢闪 (断网状态)

#endif // LED_CONTROL_H

cmd='C:/Users/<USER>/.espressif/tools/cmake/3.30.2/bin/cmake.exe;-DSDKCONFIG=C:/Users/<USER>/wifi/sdkconfig;-DIDF_PATH=C:/Users/<USER>/esp/v5.5/esp-idf;-DIDF_TARGET=esp32c3;-DPYTHON_DEPS_CHECKED=1;-DPYTHON=C:/Users/<USER>/.espressif/python_env/idf5.5_py3.11_env/Scripts/python.exe;-DEXTRA_COMPONENT_DIRS=C:/Users/<USER>/esp/v5.5/esp-idf/components/bootloader;-DPROJECT_SOURCE_DIR=C:/Users/<USER>/wifi;-DIGNORE_EXTRA_COMPONENT=;-GNinja;-S;<SOURCE_DIR><SOURCE_SUBDIR>;-B;<BINARY_DIR>'

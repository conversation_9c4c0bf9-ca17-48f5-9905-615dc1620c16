/*
 * WiFi Manager Header
 * ESP32-C3 WiFi管理模块头文件
 */

#ifndef WIFI_MANAGER_H
#define WIFI_MANAGER_H

#include <stdint.h>
#include <stdbool.h>
#include "esp_err.h"

#ifdef __cplusplus
extern "C" {
#endif

// WiFi状态枚举
typedef enum {
    STATE_INIT = 0,         // 初始化状态
    STATE_CONFIG,           // 配网状态
    STATE_CONNECTING,       // 连接中
    STATE_ONLINE,           // 在线状态
    STATE_OFFLINE,          // 离线状态
    STATE_ERROR             // 错误状态
} wifi_state_t;

/**
 * @brief 等待按键或检查配网状态
 * 这是主要的配网逻辑函数，根据config_flag决定是否进入配网模式
 */
void wait_key(void);

/**
 * @brief 获取当前WiFi状态
 * @return 当前WiFi状态
 */
wifi_state_t get_wifi_state(void);

/**
 * @brief 检查是否已配网
 * @return true 已配网, false 未配网
 */
bool is_wifi_configured(void);

/**
 * @brief 打印WiFi管理器状态
 */
void print_wifi_manager_status(void);

#ifdef __cplusplus
}
#endif

#endif // WIFI_MANAGER_H

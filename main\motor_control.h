/*
 * Motor Control Module for ESP32-C3
 * 电机控制模块 - 从Arduino移植到ESP-IDF
 */

#ifndef MOTOR_CONTROL_H
#define MOTOR_CONTROL_H

#include <stdint.h>
#include <stdbool.h>
#include "esp_err.h"
#include "freertos/FreeRTOS.h"
#include "freertos/task.h"

// 电机控制相关定义
#define MOTOR_FORWARD_PIN 5  // 控制电机正转的GPIO引脚
#define MOTOR_REVERSE_PIN 6  // 控制电机反转的GPIO引脚

// PWM相关配置
 
#define PWM_FREQ 5000    // PWM频率5kHz
#define PWM_CHANNEL 0    // 使用PWM通道0
#define PWM_RESOLUTION 8 // 8位分辨率
#define PWM_DUTY 127     // 50%占空比 (255的50%)

// 输入引脚相关配置
#define GPIO_INPUT_PIN 3    // 使用GPIO 3作为输入
#define DEBOUNCE_DELAY 100  // 去抖动延迟，单位：毫秒

// LED控制已移至独立的led_control模块

// 电机状态结构体
typedef struct {
    bool is_running;           // 电机是否正在运行
    bool direction;            // 电机方向 (true=正转, false=反转)
    uint32_t start_time;       // 电机启动时间
    uint32_t run_duration;     // 运行持续时间(毫秒)
    bool switch_state;         // 开关状态
    uint32_t last_debounce_time; // 上次去抖动时间
    volatile int switch_sign;  // 按钮中断标志
} motor_state_t;

// 电机控制函数声明
esp_err_t motor_control_init(void);
void motor_control_forward(float duration_seconds);
void motor_control_reverse(float duration_seconds);
void motor_control_stop(void);
void motor_reset(bool reverse_direction, float duration_seconds);
void motor_control_task(void *pvParameters);
void motor_check_status(void);

// 电机测试函数
void motor_startup_test(void);

// 按钮处理函数
void button_isr_handler(void *arg);
void button_check_state(void);

// 获取电机状态
motor_state_t* motor_get_state(void);

#endif // MOTOR_CONTROL_H

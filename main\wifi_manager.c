/*
 * WiFi Manager with BLE Configuration
 * ESP32-C3 WiFi管理模块，集成蓝牙配网功能
 */

#include <stdio.h>
#include <string.h>
#include "freertos/FreeRTOS.h"
#include "freertos/task.h"
#include "esp_log.h"
#include "esp_wifi.h"
#include "esp_event.h"
#include "esp_pm.h"
#include "nvs_config.h"
#include "ble_wifi_config_full.h"

static const char *TAG = "wifi_manager";

// 配网状态枚举
typedef enum {
    STATE_INIT = 0,
    STATE_CONFIG,
    STATE_CONNECTING,
    STATE_ONLINE,
    STATE_OFFLINE,
    STATE_ERROR
} wifi_state_t;

// 全局变量
static wifi_state_t current_state = STATE_INIT;
static bool is_configured = false;
// 注意：ble_config_* 变量在 ble_wifi_config_full.c 中定义，这里不重复声明

// 函数声明
static void wifi_event_handler(void* arg, esp_event_base_t event_base, int32_t event_id, void* event_data);
static void check_ble_config_status(void);
static esp_err_t init_wifi(void);
static void enable_power_save_mode(void);

/**
 * @brief WiFi事件处理函数
 */
static void wifi_event_handler(void* arg, esp_event_base_t event_base, int32_t event_id, void* event_data)
{
    if (event_base == WIFI_EVENT && event_id == WIFI_EVENT_STA_START) {
        esp_wifi_connect();
        ESP_LOGI(TAG, "WiFi started, connecting...");
        printf("WiFi started, connecting...\n");
    } else if (event_base == WIFI_EVENT && event_id == WIFI_EVENT_STA_DISCONNECTED) {
        current_state = STATE_OFFLINE;
        ESP_LOGI(TAG, "WiFi disconnected, reconnecting...");
        printf("WiFi disconnected, reconnecting...\n");
        esp_wifi_connect();
    } else if (event_base == IP_EVENT && event_id == IP_EVENT_STA_GOT_IP) {
        ip_event_got_ip_t* event = (ip_event_got_ip_t*) event_data;
        ESP_LOGI(TAG, "Got IP: " IPSTR, IP2STR(&event->ip_info.ip));
        printf("✅ WiFi连接成功！IP: " IPSTR "\n", IP2STR(&event->ip_info.ip));

        current_state = STATE_ONLINE;
        is_configured = true;

        // WiFi连接成功后启用低功耗模式
        printf("🔋 WiFi连接成功，启用低功耗模式...\n");
        enable_power_save_mode();

        // 如果正在进行蓝牙配网，设置成功状态
        if (ble_config_active) {
            printf("🎉 蓝牙配网成功！\n");
            ble_config_set_success(true);

            // 延迟停止蓝牙配网，让手机APP接收到成功消息
            vTaskDelay(3000 / portTICK_PERIOD_MS);
            ble_wifi_config_stop();
        }
    }
}

/**
 * @brief 启用低功耗模式 (仅在WiFi连接成功后调用)
 */
static void enable_power_save_mode(void)
{
    // ESP_LOGI(TAG, "Enabling power save mode after WiFi connection...");

    // // 设置WiFi省电模式
    // esp_err_t ret = esp_wifi_set_ps(WIFI_PS_MIN_MODEM);
    // if (ret == ESP_OK) {
    //     ESP_LOGI(TAG, "WiFi power save mode enabled (MIN_MODEM)");
    //     printf("✅ WiFi省电模式已启用\n");
    // } else {
    //     ESP_LOGW(TAG, "Failed to enable WiFi power save mode: %s", esp_err_to_name(ret));
    // }

    // // 设置WiFi监听间隔和信标超时
    // esp_wifi_set_inactive_time(WIFI_IF_STA, 6); // 6个信标间隔

#if CONFIG_PM_ENABLE
    // 配置CPU动态频率调节和自动轻度睡眠
    esp_pm_config_t pm_config = {
        .max_freq_mhz = 80,    // 最大CPU频率 80MHz
        .min_freq_mhz = 40,    // 最小CPU频率 10MHz
        .light_sleep_enable = true  // 启用自动轻度睡眠
    };

  esp_err_t  ret = esp_pm_configure(&pm_config);
    if (ret == ESP_OK) {
        ESP_LOGI(TAG, "Auto light sleep configured successfully");
        ESP_LOGI(TAG, "  Max CPU freq: %d MHz", pm_config.max_freq_mhz);
        ESP_LOGI(TAG, "  Min CPU freq: %d MHz", pm_config.min_freq_mhz);
        ESP_LOGI(TAG, "  Light sleep: enabled");
        printf("✅ 自动轻度睡眠已启用 (10-80MHz)\n");
    } else {
        ESP_LOGW(TAG, "Failed to configure power management: %s", esp_err_to_name(ret));
        printf("⚠️ 自动轻度睡眠配置失败\n");
    }
#else
    ESP_LOGW(TAG, "Power management not enabled in configuration");
    printf("⚠️ 电源管理未在配置中启用\n");
#endif

    printf("🔋 低功耗模式配置完成\n");
}

/**
 * @brief 蓝牙配网状态检查
 */
static void check_ble_config_status(void)
{
    ble_config_state_t state = ble_config_get_state();

    switch (state) {
        case BLE_CONFIG_STATE_CONNECTED:
            printf("📱 手机APP已连接\n");
            break;

        case BLE_CONFIG_STATE_SUCCESS:
            {
                ble_wifi_config_data_t *data = ble_config_get_wifi_data();
                if (data && data->valid) {
                    printf("📥 收到WiFi配置:\n");
                    printf("   SSID: %s\n", data->ssid);
                    printf("   密码: ***\n");

                    // 保存WiFi配置到NVS
                    set_wifi_config(data->ssid, data->password);

                    // 尝试连接WiFi
                    printf("🔄 正在连接WiFi...\n");
                    current_state = STATE_CONNECTING;
                    init_wifi();
                }
            }
            break;

        case BLE_CONFIG_STATE_TIMEOUT:
            printf("⏰ 蓝牙配网超时\n");
            ble_config_active = false;
            break;

        case BLE_CONFIG_STATE_FAILED:
            printf("❌ 蓝牙配网失败\n");
            ble_config_active = false;
            break;

        default:
            break;
    }
}

/**
 * @brief 初始化WiFi连接
 */
static esp_err_t init_wifi(void)
{
    // 初始化网络接口
    ESP_ERROR_CHECK(esp_netif_init());
    ESP_ERROR_CHECK(esp_event_loop_create_default());
    esp_netif_create_default_wifi_sta();
    
    // 初始化WiFi
    wifi_init_config_t cfg = WIFI_INIT_CONFIG_DEFAULT();
    ESP_ERROR_CHECK(esp_wifi_init(&cfg));
    
    // 注册事件处理函数
    ESP_ERROR_CHECK(esp_event_handler_instance_register(WIFI_EVENT, ESP_EVENT_ANY_ID, &wifi_event_handler, NULL, NULL));
    ESP_ERROR_CHECK(esp_event_handler_instance_register(IP_EVENT, IP_EVENT_STA_GOT_IP, &wifi_event_handler, NULL, NULL));
    
    // 配置WiFi
    wifi_config_t wifi_config = {
        .sta = {
            .threshold.authmode = WIFI_AUTH_WPA2_PSK,
        },
    };
    
    // 使用NVS中的WiFi配置
    strcpy((char*)wifi_config.sta.ssid, config.stassid);
    strcpy((char*)wifi_config.sta.password, config.stapsw);
    
    ESP_ERROR_CHECK(esp_wifi_set_mode(WIFI_MODE_STA));
    ESP_ERROR_CHECK(esp_wifi_set_config(WIFI_IF_STA, &wifi_config));
    ESP_ERROR_CHECK(esp_wifi_start());
    
    printf("WiFi初始化完成，SSID: %s\n", config.stassid);
    return ESP_OK;
}

/**
 * @brief 等待按键或检查配网状态 - 主要配网逻辑
 */
void wait_key(void)
{
    printf("\n=== 配网判断逻辑 ===\n");
    printf("config_flag: %d\n", config_flag);
    printf("WiFi SSID: '%s' (长度: %d)\n", config.stassid, strlen(config.stassid));
    printf("魔数: 0x%02X\n", config.magic);

    // 判断是否需要配网的条件：
    // 1. config_flag == 1 (配置无效或SSID为空)
    // 2. WiFi SSID为空
    bool need_config = (config_flag == 1) || (strlen(config.stassid) == 0);

    printf("需要配网: %s\n", need_config ? "是" : "否");
    printf("==================\n");

    if (need_config) {
        printf("🔧 进入配网模式 - 原因: ");
        if (config_flag == 1) {
            printf("配置标志为1\n");
        }
        if (strlen(config.stassid) == 0) {
            printf("WiFi SSID为空\n");
        }
        
        // 停止WiFi（如果正在运行）
        esp_wifi_stop();
        esp_wifi_deinit();
        vTaskDelay(100 / portTICK_PERIOD_MS);
        
        // 初始化蓝牙配网模块
        printf("🔧 初始化蓝牙配网模块...\n");
        esp_err_t ret = ble_wifi_config_init(macStr);
        if (ret != ESP_OK) {
            printf("❌ 蓝牙配网初始化失败: %s\n", esp_err_to_name(ret));
            return;
        }
        printf("✅ 蓝牙配网模块初始化成功\n");

        // 启动蓝牙配网 (内部会设置ble_config_active状态)
        printf("🚀 启动蓝牙配网...\n");
        ret = ble_wifi_config_start();
        if (ret != ESP_OK) {
            printf("❌ 蓝牙配网启动失败: %s\n", esp_err_to_name(ret));
            return;
        }
        printf("✅ 蓝牙配网启动成功\n");
        current_state = STATE_CONFIG;
        is_configured = false; // 设置为未配网状态
        
        // 等待配网完成或超时
        while (ble_config_active) {
            vTaskDelay(1000 / portTICK_PERIOD_MS);

            // 检查配网状态
            check_ble_config_status();

            // 检查超时
            if (ble_config_is_timeout()) {
                printf("⏰ 配网超时，停止蓝牙配网\n");
                ble_wifi_config_stop();
                ble_config_active = false;
                break;
            }

            // 检查配网成功
            if (ble_config_is_success() && current_state == STATE_ONLINE) {
                printf("🎉 配网成功完成！\n");
                break;
            }
        }

        // 清理蓝牙资源
        ble_wifi_config_deinit();

    } else {
        printf("✅ 已有有效配置，直接连接WiFi...\n");
        printf("   SSID: %s\n", config.stassid);
        printf("   密码: %s\n", strlen(config.stapsw) > 0 ? "已设置" : "未设置");
        init_wifi();
        
        // 等待WiFi连接
        int retry_count = 0;
        while (current_state != STATE_ONLINE && retry_count < 30) {
            vTaskDelay(1000 / portTICK_PERIOD_MS);
            retry_count++;
            printf("等待WiFi连接... (%d/30)\n", retry_count);
        }
        
        if (current_state == STATE_ONLINE) {
            printf("✅ WiFi连接成功！\n");
            is_configured = true;
        } else {
            printf("❌ WiFi连接失败，可能需要重新配网\n");
            // 可以选择重新进入配网模式
            config_flag = 1;
            save_config(); // 保存config_flag状态
        }
    }
}

/**
 * @brief 获取当前WiFi状态
 */
wifi_state_t get_wifi_state(void)
{
    return current_state;
}

/**
 * @brief 检查是否已配网
 */
bool is_wifi_configured(void)
{
    return is_configured;
}

/**
 * @brief 打印WiFi管理器状态
 */
void print_wifi_manager_status(void)
{
    const char* state_strings[] = {"INIT", "CONFIG", "CONNECTING", "ONLINE", "OFFLINE", "ERROR"};
    
    printf("\n=== WiFi Manager Status ===\n");
    printf("State: %s\n", state_strings[current_state]);
    printf("Configured: %s\n", is_configured ? "Yes" : "No");
    printf("Config Flag: %d\n", config_flag);
    printf("BLE Config Active: %s\n", ble_config_active ? "Yes" : "No");
    printf("BLE Config Success: %s\n", ble_config_success ? "Yes" : "No");
    printf("WiFi SSID: %s\n", config.stassid);
    printf("==========================\n");
}

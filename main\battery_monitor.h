/*
 * Battery Monitor Module for ESP32-C3
 * 电池监测模块 - 18650双电池监测
 */

#ifndef BATTERY_MONITOR_H
#define BATTERY_MONITOR_H

#include <stdint.h>
#include <stdbool.h>
#include "esp_err.h"
#include "esp_adc/adc_oneshot.h"

// 电池监测配置
#define BATTERY_ADC_CHANNEL     ADC_CHANNEL_0  // GPIO0用于电池电压检测
#define BATTERY_ADC_ATTEN       ADC_ATTEN_DB_6 // 6dB衰减
#define BATTERY_ADC_BITWIDTH    ADC_BITWIDTH_12 // 12位分辨率

// 电池参数配置
#define BATTERY_MIN_VOLTAGE     6.0f    // 最低电压 (2S 18650: 3.0V * 2)
#define BATTERY_MAX_VOLTAGE     8.4f    // 最高电压 (2S 18650: 4.2V * 2)
#define VOLTAGE_DIVIDER_RATIO   3.0f    // 分压比 (假设使用3:1分压)
#define BATTERY_LOW_THRESHOLD   20      // 低电量阈值 (%)

// 电池状态结构体
typedef struct {
    float voltage;              // 电池电压 (V)
    int percentage;             // 电池电量百分比
    bool is_low;               // 是否低电量
    bool is_charging;          // 是否正在充电
    char health_status[16];    // 健康状态
    uint32_t last_measure_time; // 上次测量时间
    bool first_measure_done;   // 是否完成首次测量
} battery_status_t;

// 电池监测函数声明
esp_err_t battery_monitor_init(void);
esp_err_t battery_read_voltage(float *voltage);
esp_err_t battery_update_status(void);
int battery_voltage_to_percentage(float voltage);
const char* battery_get_health_status(int percentage);
esp_err_t battery_publish_status(void);
battery_status_t* battery_get_status(void);

// 电池监测任务
void battery_monitor_task(void *pvParameters);

#endif // BATTERY_MONITOR_H

/*
 * BLE WiFi Configuration Module for ESP32-C3
 * 完整的蓝牙WiFi配网模块 - 保留所有功能和数据处理
 */

#ifndef BLE_WIFI_CONFIG_FULL_H
#define BLE_WIFI_CONFIG_FULL_H

#include <stdint.h>
#include <stdbool.h>
#include <string.h>
#include "esp_err.h"
#include "freertos/FreeRTOS.h"
#include "freertos/task.h"

// 条件包含蓝牙头文件

#include "esp_bt.h"
#include "esp_gap_ble_api.h"
#include "esp_gatts_api.h"
#include "esp_bt_main.h"
#include "esp_gatt_common_api.h"
#include "esp_gatt_defs.h"
#endif

#ifdef __cplusplus
extern "C" {
#endif

// 服务和特征 UUID (与Arduino版本保持一致)
#define BLE_SERVICE_UUID        "6E400001-B5A3-F393-E0A9-E50E24DCCA9E"
#define BLE_CMD_CHAR_UUID       "6E400002-B5A3-F393-E0A9-E50E24DCCA9E"
#define BLE_DATA_CHAR_UUID      "6E400003-B5A3-F393-E0A9-E50E24DCCA9E"

// 消息类型定义 (与Arduino版本保持一致)
typedef enum {
    MSG_COMMAND = 0x01,      // 命令消息
    MSG_RESPONSE = 0x02,     // 响应消息
    MSG_DATA = 0x03,         // 数据传输
    MSG_CONFIRM = 0x04       // 确认包
} ble_message_type_t;

// 子类型定义 (与Arduino版本保持一致)
typedef enum {
    // 命令子类型
    CMD_GET_DEVICE_INFO = 0x01,  // 获取设备信息
    CMD_SCAN_WIFI = 0x02,        // 扫描WiFi网络
    CMD_SET_WIFI = 0x03,         // 设置WiFi信息
    CMD_CONNECT_WIFI = 0x04,     // 连接WiFi
    CMD_RESTART = 0x05,          // 重启设备
    
    // 响应子类型
    RESP_DEVICE_INFO = 0x01,     // 设备信息
    RESP_WIFI_LIST = 0x02,       // WiFi列表
    RESP_WIFI_STATUS = 0x03,     // WiFi状态
    RESP_ACK = 0x04,             // 通用确认
    RESP_ERROR = 0x05,           // 错误信息
    
    // 数据子类型
    DATA_SSID = 0x01,            // WiFi SSID
    DATA_PASSWORD = 0x02,        // WiFi 密码
    DATA_CONFIG = 0x03,          // 其他配置数据
    
    // 确认子类型
    CONFIRM_RECEIVED = 0x01,     // 数据已接收
    CONFIRM_COMPLETE = 0x02      // 传输完成
} ble_sub_type_t;

// 数据包头结构 (与Arduino版本完全一致)
typedef struct {
    uint8_t type;       // 消息类型
    uint8_t subType;    // 子类型
    uint8_t flags;      // 标志位(加密、分片等)
    uint8_t seq;        // 序列号
    uint16_t length;    // 数据长度 (网络字节序)
    uint16_t crc;       // CRC校验和 (网络字节序)
} __attribute__((packed)) ble_packet_header_t;

// BLE配网状态
typedef enum {
    BLE_CONFIG_STATE_IDLE = 0,
    BLE_CONFIG_STATE_ADVERTISING,
    BLE_CONFIG_STATE_CONNECTED,
    BLE_CONFIG_STATE_CONFIGURING,
    BLE_CONFIG_STATE_SUCCESS,
    BLE_CONFIG_STATE_FAILED,
    BLE_CONFIG_STATE_TIMEOUT
} ble_config_state_t;

// WiFi配网数据
typedef struct {
    char ssid[32];
    char password[64];
    bool valid;
} ble_wifi_config_data_t;

// 接收缓冲区大小
#define BLE_RECEIVE_BUFFER_SIZE 512
#define BLE_MAX_PACKET_SIZE 20

// BLE配网上下文结构
typedef struct {
    bool device_connected;
    bool config_success;
    ble_config_state_t state;

    // 协议相关
    uint8_t seq;
    bool expecting_more_data;
    uint16_t expected_total_length;
    uint8_t last_packet_type;
    
    // 数据缓冲区
    uint8_t receive_buffer[BLE_RECEIVE_BUFFER_SIZE];
    size_t receive_buffer_len;
    
    // WiFi配置数据
    ble_wifi_config_data_t wifi_config;
    
    // GATT相关
    uint16_t gatts_if;
    uint16_t conn_id;
    uint16_t service_handle;
    uint16_t cmd_char_handle;
    uint16_t data_char_handle;
    
} ble_wifi_config_context_t;

// 全局变量声明
extern ble_wifi_config_context_t ble_config_ctx;
extern bool ble_config_active;
extern bool ble_config_success;
extern uint32_t ble_config_start_time;

// 配网超时时间（毫秒）
#define BLE_CONFIG_TIMEOUT_MS (5 * 60 * 1000)  // 5分钟

// 函数声明

/**
 * @brief 初始化BLE WiFi配网模块
 * @param device_name 设备名称
 * @return ESP_OK 成功, 其他值表示错误
 */
esp_err_t ble_wifi_config_init(const char *device_name);

/**
 * @brief 启动BLE WiFi配网
 * @return ESP_OK 成功, 其他值表示错误
 */
esp_err_t ble_wifi_config_start(void);

/**
 * @brief 停止BLE WiFi配网
 * @return ESP_OK 成功, 其他值表示错误
 */
esp_err_t ble_wifi_config_stop(void);

/**
 * @brief 反初始化BLE WiFi配网模块
 * @return ESP_OK 成功, 其他值表示错误
 */
esp_err_t ble_wifi_config_deinit(void);

/**
 * @brief 更新BLE配网状态
 */
void ble_wifi_config_update(void);

/**
 * @brief 检查配网是否超时
 * @return true 超时, false 未超时
 */
bool ble_config_is_timeout(void);

/**
 * @brief 获取配网状态
 * @return 当前配网状态
 */
ble_config_state_t ble_config_get_state(void);

/**
 * @brief 设置配网成功状态
 * @param success 是否成功
 */
void ble_config_set_success(bool success);

/**
 * @brief 获取配网成功状态
 * @return true 成功, false 失败
 */
bool ble_config_is_success(void);

/**
 * @brief 获取接收到的WiFi配置
 * @return WiFi配置数据指针
 */
ble_wifi_config_data_t* ble_config_get_wifi_data(void);

/**
 * @brief 计算CRC16校验和
 * @param data 数据指针
 * @param length 数据长度
 * @return CRC16值
 */
uint16_t ble_calculate_crc16(const uint8_t* data, size_t length);

/**
 * @brief 发送数据包
 * @param type 消息类型
 * @param sub_type 子类型
 * @param data 数据指针
 * @param length 数据长度
 * @param more_fragments 是否有更多分片
 * @return ESP_OK 成功, 其他值表示错误
 */
esp_err_t ble_send_packet(uint8_t type, uint8_t sub_type, const uint8_t* data, size_t length, bool more_fragments);

/**
 * @brief 处理接收到的数据包
 * @param data 数据指针
 * @param length 数据长度
 */
void ble_process_received_packet(const uint8_t* data, size_t length);

/**
 * @brief 发送设备信息
 */
void ble_send_device_info(void);

/**
 * @brief 扫描WiFi网络
 */
void ble_scan_wifi_networks(void);

/**
 * @brief 连接到WiFi
 */
void ble_connect_to_wifi(void);

/**
 * @brief 发送响应
 * @param sub_type 子类型
 * @param message 消息内容
 */
void ble_send_response(uint8_t sub_type, const char* message);

/**
 * @brief 发送错误响应
 * @param error_message 错误消息
 */
void ble_send_error_response(const char* error_message);

/**
 * @brief 打印BLE配网状态
 */
void ble_config_print_status(void);

/**
 * @brief 处理完整的消息
 * @param type 消息类型
 * @param sub_type 子类型
 * @param data 数据指针
 * @param length 数据长度
 */
void ble_handle_complete_message(uint8_t type, uint8_t sub_type, const uint8_t* data, size_t length);

/**
 * @brief 处理命令
 * @param sub_type 子类型
 * @param data 数据指针
 * @param length 数据长度
 */
void ble_handle_command(uint8_t sub_type, const uint8_t* data, size_t length);

/**
 * @brief 处理数据
 * @param sub_type 子类型
 * @param data 数据指针
 * @param length 数据长度
 */
void ble_handle_data(uint8_t sub_type, const uint8_t* data, size_t length);

#ifdef __cplusplus
}
#endif



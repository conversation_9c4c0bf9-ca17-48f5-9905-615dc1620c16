/*
 * ESP32-C3 WiFi Power Save Example with Auto Light Sleep
 * Based on ESP-IDF official power save example
 */

#include <stdio.h>
#include <string.h>
#include "sdkconfig.h"
#include "freertos/FreeRTOS.h"
#include "freertos/task.h"
#include "freertos/event_groups.h"
#include "esp_wifi.h"
#include "esp_log.h"
#include "esp_event.h"
#include "esp_pm.h"
#include "esp_netif.h"
#include "nvs_config.h"
#include "mqtt_client.h"
#include "wifi_manager.h"
#include "esp_mac.h"
#include "motor_control.h"
#include "mqtt_handler.h"
#include "led_control.h"
#include "cJSON.h"
#include <math.h>

// WiFi配置
#define WIFI_SSID      "HanRui_2.4G"          // 请修改为您的WiFi名称
#define WIFI_PASS      "77889900" // 请修改为您的WiFi密码
#define DEFAULT_LISTEN_INTERVAL 3
#define DEFAULT_BEACON_TIMEOUT  6

// 电源管理配置
#define DEFAULT_PS_MODE WIFI_PS_MIN_MODEM  // WiFi省电模式
#define MAX_CPU_FREQ_MHZ 80                // 最大CPU频率
#define MIN_CPU_FREQ_MHZ 80                // 最小CPU频率

// MQTT配置 - 请根据您的MQTT服务器修改
#define MQTT_BROKER_URL "mqtt://wechat.taianhengrun.com"  // 公共MQTT服务器，您可以替换为自己的
#define MQTT_PORT 1883
// MQTT配置现在完全从nvs_config动态获取
// 客户端ID和用户名使用设备SN，密码使用全局mqpwd变量
// 动态MQTT主题 - 基于设备SN
static char mqtt_subscribe_topic[64];  // controller/[SN]/device

static const char *TAG = "power_save";

// MQTT状态监控变量
static bool mqtt_loop_running = false;
static uint32_t mqtt_reconnect_count = 0;
static uint32_t last_mqtt_check_time = 0;

// 电池监测变量
static bool first_battery_measure_done = false;
static uint32_t last_battery_check_time = 0;
static float battery_voltage = 0.0f;
static int battery_percentage = 0;

#define MQTT_CHECK_INTERVAL_MS 30000   // 每30秒检查一次MQTT状态
#define MQTT_RECONNECT_MAX_RETRY 10   // MQTT最大重连次数
#define BATTERY_CHECK_INTERVAL_MS 3600000  // 每小时检查一次电池 (3600秒)

// 验证MQTT配置
static bool validate_mqtt_config(void) {
    extern char mqpwd[64];
    bool valid = true;

    ESP_LOGI(TAG, "🔍 验证MQTT配置...");

    if (strlen(config.mqtt_broker) == 0) {
        ESP_LOGE(TAG, "❌ MQTT服务器地址为空！");
        valid = false;
    } else {
        ESP_LOGI(TAG, "✅ MQTT服务器: %s", config.mqtt_broker);
    }

    if (config.mqtt_port == 0) {
        ESP_LOGE(TAG, "❌ MQTT端口未设置！");
        valid = false;
    } else {
        ESP_LOGI(TAG, "✅ MQTT端口: %d", config.mqtt_port);
    }

    if (strlen(config.sn) == 0) {
        ESP_LOGE(TAG, "❌ 设备SN为空！(用作客户端ID和用户名)");
        valid = false;
    } else {
        ESP_LOGI(TAG, "✅ 设备SN: %s (用作客户端ID和用户名)", config.sn);
    }

    if (strlen(mqpwd) == 0) {
        ESP_LOGW(TAG, "⚠️ MQTT密码为空！");
    } else {
        ESP_LOGI(TAG, "✅ 密码: 已设置 (长度: %d)", strlen(mqpwd));
    }

    return valid;
}

// 监控任务函数声明（实现在变量定义之后）
void mqtt_wifi_monitor_task(void *pvParameters);

// 简单的电池监测函数
static float read_battery_voltage(void)
{
    // 模拟电池电压读取 (实际项目中需要配置ADC)
    // 这里返回一个模拟值，您可以根据实际硬件配置ADC
    static float simulated_voltage = 7.4f; // 模拟18650双电池电压

    // 添加一些随机变化模拟真实电池
    simulated_voltage -= 0.001f; // 模拟电池缓慢放电
    if (simulated_voltage < 6.0f) {
        simulated_voltage = 8.4f; // 模拟充电
    }

    return simulated_voltage;
}

static int voltage_to_percentage(float voltage)
{
    // 18650双电池 (2S): 6.0V-8.4V
    const float min_voltage = 6.0f;
    const float max_voltage = 8.4f;

    if (voltage <= min_voltage) {
        return 0;
    } else if (voltage >= max_voltage) {
        return 100;
    } else {
        return (int)((voltage - min_voltage) / (max_voltage - min_voltage) * 100);
    }
}

static void perform_battery_measurement(void)
{
    ESP_LOGI(TAG, "🔋 开始电池电量检测...");

    battery_voltage = read_battery_voltage();
    battery_percentage = voltage_to_percentage(battery_voltage);

    ESP_LOGI(TAG, "🔋 电池电压: %.3fV", battery_voltage);
    ESP_LOGI(TAG, "🔋 电池电量: %d%%", battery_percentage);

    // 判断电池状态
    const char* status;
    if (battery_percentage >= 75) {
        status = "excellent";
    } else if (battery_percentage >= 50) {
        status = "good";
    } else if (battery_percentage >= 25) {
        status = "fair";
    } else if (battery_percentage >= 10) {
        status = "poor";
    } else {
        status = "critical";
    }

    ESP_LOGI(TAG, "🔋 电池健康状态: %s", status);

    if (!first_battery_measure_done) {
        first_battery_measure_done = true;
        ESP_LOGI(TAG, "✅ 首次电池测量完成");
    }
}

// 函数声明，实现在变量定义之后
static void publish_battery_status(void);

// 停止MQTT和WiFi监控任务
void stop_mqtt_wifi_monitor(void)
{
    ESP_LOGI(TAG, "🛑 停止MQTT和WiFi监控任务");
    mqtt_loop_running = false;
}

// 构建MQTT订阅主题
static void build_mqtt_subscribe_topic(void) {
    // 使用nvs_config中的全局配置获取设备SN
    extern device_config_t config;

    // 构建主题：controller/[SN]/device
    snprintf(mqtt_subscribe_topic, sizeof(mqtt_subscribe_topic), "controller/%s/device", config.sn);

    ESP_LOGI(TAG, "📋 MQTT订阅主题构建完成: %s", mqtt_subscribe_topic);
}


static esp_ip4_addr_t current_ip = {0};
static esp_mqtt_client_handle_t mqtt_client = NULL;
static bool mqtt_connected = false;
static bool wifi_connected = false;

// MQTT事件处理函数
static void mqtt_event_handler(void *handler_args, esp_event_base_t base, int32_t event_id, void *event_data)
{
    esp_mqtt_event_handle_t event = event_data;
    esp_mqtt_client_handle_t client = event->client;

    switch ((esp_mqtt_event_id_t)event_id) {
    case MQTT_EVENT_CONNECTED:
        ESP_LOGI(TAG, "🎉 MQTT_EVENT_CONNECTED - 连接成功！");
        printf("🎉 MQTT connected to broker successfully!\n");
        mqtt_connected = true;

        // 记录连接时间用于分析连接持续时间
        static uint32_t connect_time = 0;
        connect_time = xTaskGetTickCount() * portTICK_PERIOD_MS;

        // 输出MQTT连接状态
        ESP_LOGI(TAG, "✅ MQTT连接成功！");
        ESP_LOGI(TAG, "📡 MQTT服务器: %s:%d", config.mqtt_broker, config.mqtt_port);
        ESP_LOGI(TAG, "🆔 客户端ID: %s", config.sn);
        ESP_LOGI(TAG, "👤 用户名: %s", config.sn);

        // 构建并订阅动态主题
        build_mqtt_subscribe_topic();

        // 延迟一下再订阅，确保连接稳定
        vTaskDelay(pdMS_TO_TICKS(100));

        // 订阅主题：controller/[SN]/device
        int msg_id = esp_mqtt_client_subscribe(client, mqtt_subscribe_topic, 1);
        if (msg_id >= 0) {
            ESP_LOGI(TAG, "✅ 成功发送订阅请求: %s (msg_id=%d)", mqtt_subscribe_topic, msg_id);
            printf("📡 订阅主题: %s\n", mqtt_subscribe_topic);
        } else {
            ESP_LOGE(TAG, "❌ 订阅主题失败: %s", mqtt_subscribe_topic);
        }

        // 发布设备上线消息到状态主题
        char online_topic[128];
        snprintf(online_topic, sizeof(online_topic), "controller/%s/status", config.sn);

        // 创建详细的上线状态消息
        char online_msg[256];
        snprintf(online_msg, sizeof(online_msg),
                "{\"status\":\"online\",\"sn\":\"%.17s\",\"ip\":\"%d.%d.%d.%d\",\"timestamp\":%lu}",
                config.sn, IP2STR(&current_ip), xTaskGetTickCount() * portTICK_PERIOD_MS / 1000);

        esp_mqtt_client_publish(client, online_topic, online_msg, 0, 1, 1);
        ESP_LOGI(TAG, "📤 发布上线状态到: %s", online_topic);
        ESP_LOGI(TAG, "📤 上线消息: %s", online_msg);

        // MQTT初次连接成功后立即进行电池检测
        if (!first_battery_measure_done) {
            ESP_LOGI(TAG, "🔋 MQTT连接成功，准备执行首次电池测量...");
            vTaskDelay(pdMS_TO_TICKS(500)); // 稍等片刻确保连接稳定
            perform_battery_measurement();

            // 发布首次电池状态
            vTaskDelay(pdMS_TO_TICKS(1000)); // 等待1秒确保测量完成
            publish_battery_status();
        }

        // MQTT连接成功后，LED熄灭 (表示网络正常)
        ESP_LOGI(TAG, "💡 MQTT连接成功，LED熄灭");
        led_set_off();
        break;

    case MQTT_EVENT_DISCONNECTED:
        ESP_LOGI(TAG, "MQTT_EVENT_DISCONNECTED");
        printf("MQTT disconnected from broker\n");
        mqtt_connected = false;

        // 输出MQTT断开连接状态
        ESP_LOGW(TAG, "❌ MQTT连接断开！");
        ESP_LOGW(TAG, "📡 服务器: %s:%d", config.mqtt_broker, config.mqtt_port);
        ESP_LOGI(TAG, "🔄 将自动重连...");

        // LED控制由WiFi事件处理，MQTT断开不单独控制LED

        // 立即尝试重连（如果WiFi正常）
        if (wifi_connected) {
            ESP_LOGI(TAG, "🔄 WiFi正常，立即尝试MQTT重连");
            vTaskDelay(pdMS_TO_TICKS(2000));  // 等待2秒后重连
            esp_mqtt_client_reconnect(client);
        }
        break;

    case MQTT_EVENT_SUBSCRIBED:
        ESP_LOGI(TAG, "🎯 MQTT_EVENT_SUBSCRIBED - 订阅成功！");
        ESP_LOGI(TAG, "📋 消息ID: %d", event->msg_id);
        ESP_LOGI(TAG, "✅ 设备现在可以接收命令了！");
        printf("🎯 订阅成功！设备现在可以接收MQTT命令\n");
        break;

    case MQTT_EVENT_UNSUBSCRIBED:
        ESP_LOGI(TAG, "MQTT_EVENT_UNSUBSCRIBED, msg_id=%d", event->msg_id);
        break;

    case MQTT_EVENT_PUBLISHED:
        ESP_LOGI(TAG, "MQTT_EVENT_PUBLISHED, msg_id=%d", event->msg_id);
        break;

    case MQTT_EVENT_DATA:
        ESP_LOGI(TAG, "MQTT_EVENT_DATA");
        printf("MQTT message received:\n");
        printf("Topic: %.*s\n", event->topic_len, event->topic);
        printf("Data: %.*s\n", event->data_len, event->data);

        // 检查是否是我们订阅的主题
        if (strncmp(event->topic, mqtt_subscribe_topic, event->topic_len) == 0) {
            // 创建以null结尾的字符串
            char *topic_str = malloc(event->topic_len + 1);
            char *data_str = malloc(event->data_len + 1);

            if (topic_str != NULL && data_str != NULL) {
                memcpy(topic_str, event->topic, event->topic_len);
                topic_str[event->topic_len] = '\0';

                memcpy(data_str, event->data, event->data_len);
                data_str[event->data_len] = '\0';

                // 调用mqtt_handler中的统一处理函数
                mqtt_handle_message(topic_str, (uint8_t*)data_str, event->data_len);

                free(topic_str);
                free(data_str);
            }
        }
        break;

    case MQTT_EVENT_ERROR:
        ESP_LOGE(TAG, "❌ MQTT_EVENT_ERROR - 连接错误！");
        printf("❌ MQTT error occurred\n");
        ESP_LOGE(TAG, "🔍 错误类型: %d", event->error_handle->error_type);
        ESP_LOGE(TAG, "🔍 连接错误代码: %d", event->error_handle->connect_return_code);

        // 详细的错误分析
        if (event->error_handle->error_type == MQTT_ERROR_TYPE_TCP_TRANSPORT) {
            ESP_LOGE(TAG, "🌐 TCP传输错误，检查网络连接和服务器地址");
        } else if (event->error_handle->error_type == MQTT_ERROR_TYPE_CONNECTION_REFUSED) {
            ESP_LOGE(TAG, "🚫 MQTT连接被拒绝，可能的原因:");
            ESP_LOGE(TAG, "   - 用户名错误: %s", config.sn);
            ESP_LOGE(TAG, "   - 密码错误: %s", mqpwd);
            ESP_LOGE(TAG, "   - 客户端ID冲突: %s", config.sn);
            ESP_LOGE(TAG, "   - 服务器权限设置问题");

            // 显示连接返回码的含义
            switch (event->error_handle->connect_return_code) {
                case 1: ESP_LOGE(TAG, "   返回码1: 协议版本不支持"); break;
                case 2: ESP_LOGE(TAG, "   返回码2: 客户端ID被拒绝"); break;
                case 3: ESP_LOGE(TAG, "   返回码3: 服务器不可用"); break;
                case 4: ESP_LOGE(TAG, "   返回码4: 用户名或密码错误"); break;
                case 5: ESP_LOGE(TAG, "   返回码5: 未授权"); break;
                default: ESP_LOGE(TAG, "   未知返回码: %d", event->error_handle->connect_return_code); break;
            }
        }
        break;

    default:
        ESP_LOGI(TAG, "Other MQTT event id:%d", event->event_id);
        break;
    }
}

// WiFi事件处理函数
static void event_handler(void* arg, esp_event_base_t event_base,
                         int32_t event_id, void* event_data)
{
    if (event_base == WIFI_EVENT && event_id == WIFI_EVENT_STA_START) {
        esp_wifi_connect();
        ESP_LOGI(TAG, "WiFi started, connecting...");
    } else if (event_base == WIFI_EVENT && event_id == WIFI_EVENT_STA_DISCONNECTED) {
        esp_wifi_connect();
        ESP_LOGI(TAG, "WiFi disconnected, reconnecting...");
        current_ip.addr = 0; // 清除IP地址
        wifi_connected = false;
        mqtt_connected = false;

        // WiFi断开连接时LED慢闪
        ESP_LOGI(TAG, "💡 WiFi断开连接，LED慢闪");
        led_set_slow_blink();
    } else if (event_base == IP_EVENT && event_id == IP_EVENT_STA_GOT_IP) {
        ip_event_got_ip_t* event = (ip_event_got_ip_t*) event_data;
        ESP_LOGI(TAG, "Got IP: " IPSTR, IP2STR(&event->ip_info.ip));
        current_ip = event->ip_info.ip; // 保存IP地址
        wifi_connected = true;
        printf("WiFi connected! IP: " IPSTR "\n", IP2STR(&event->ip_info.ip));

        // WiFi连接成功后LED熄灭
        ESP_LOGI(TAG, "💡 WiFi连接成功，LED熄灭");
        led_set_off();

        // WiFi连接成功后启动MQTT客户端
        if (mqtt_client != NULL) {
            ESP_LOGI(TAG, "🚀 WiFi连接成功，正在启动MQTT客户端...");
            ESP_LOGI(TAG, "📡 尝试连接到: %s:%d", config.mqtt_broker, config.mqtt_port);

            // 最终检查认证信息
            extern char mqpwd[64];
            ESP_LOGI(TAG, "🔐 连接认证信息:");
            ESP_LOGI(TAG, "   客户端ID: '%s'", config.sn);
            ESP_LOGI(TAG, "   用户名: '%s'", config.sn);
            ESP_LOGI(TAG, "   密码: '%s' (长度: %d)", mqpwd, strlen(mqpwd));

            esp_err_t ret = esp_mqtt_client_start(mqtt_client);
            if (ret == ESP_OK) {
                ESP_LOGI(TAG, "✅ MQTT客户端启动成功，等待连接结果...");
            } else {
                ESP_LOGE(TAG, "❌ MQTT客户端启动失败: %s", esp_err_to_name(ret));
            }
        } else {
            ESP_LOGE(TAG, "❌ MQTT客户端未初始化！");
        }
    }
}

// 初始化MQTT客户端
static void mqtt_app_start(void)
{
    ESP_LOGI(TAG, "🔧 开始初始化MQTT客户端...");

    // 调试：显示完整配置信息
    ESP_LOGI(TAG, "🔍 当前配置信息:");
    ESP_LOGI(TAG, "  SN: '%s' (长度: %d)", config.sn, strlen(config.sn));
    ESP_LOGI(TAG, "  MQTT服务器: '%s'", config.mqtt_broker);
    ESP_LOGI(TAG, "  MQTT端口: %d", config.mqtt_port);
    ESP_LOGI(TAG, "  魔数: 0x%02X", config.magic);

    // 验证MQTT配置
    if (!validate_mqtt_config()) {
        ESP_LOGE(TAG, "❌ MQTT配置验证失败，无法启动MQTT客户端");

        // 如果SN为空，尝试使用默认值
        if (strlen(config.sn) == 0) {
            ESP_LOGW(TAG, "⚠️ SN为空，使用默认SN");
            strcpy(config.sn, "ESP32C3_DEFAULT");
            save_config();  // 保存默认SN
            ESP_LOGI(TAG, "✅ 已设置默认SN: %s", config.sn);
        } else {
            return;
        }
    }

    // 使用外部全局变量
    extern char mqpwd[64];

    // 调试：检查mqpwd的值
    ESP_LOGI(TAG, "🔑 MQTT密码调试信息:");
    ESP_LOGI(TAG, "  mqpwd地址: %p", mqpwd);
    ESP_LOGI(TAG, "  mqpwd长度: %d", strlen(mqpwd));
    ESP_LOGI(TAG, "  mqpwd内容: '%s'", mqpwd);
    ESP_LOGI(TAG, "  mqpwd前3字符: %02X %02X %02X",
             (unsigned char)mqpwd[0], (unsigned char)mqpwd[1], (unsigned char)mqpwd[2]);

    // 构建完整的MQTT URI
    char mqtt_uri[256];
    if (strstr(config.mqtt_broker, "mqtt://") == NULL) {
        // 如果没有协议前缀，添加它
        snprintf(mqtt_uri, sizeof(mqtt_uri), "mqtt://%s:%d", config.mqtt_broker, config.mqtt_port);
    } else {
        // 如果已有协议前缀，直接使用
        strncpy(mqtt_uri, config.mqtt_broker, sizeof(mqtt_uri) - 1);
        mqtt_uri[sizeof(mqtt_uri) - 1] = '\0';
    }

    ESP_LOGI(TAG, "🔗 MQTT URI: %s", mqtt_uri);

    esp_mqtt_client_config_t mqtt_cfg = {
        .broker.address.uri = mqtt_uri,
        .credentials.client_id = config.sn,        // 直接使用SN作为客户端ID
        .credentials.username = config.sn,         // 使用SN作为用户名
        .credentials.authentication.password = mqpwd,  // 使用全局mqpwd变量
        .session.keepalive = 120,                  // 增加心跳间隔到2分钟
        .session.disable_clean_session = false,
        .network.timeout_ms = 30000,               // 增加超时时间到30秒
        .network.refresh_connection_after_ms = 0,  // 禁用自动刷新连接
        .network.reconnect_timeout_ms = 10000,     // 重连间隔10秒
        .network.disable_auto_reconnect = false,   // 启用自动重连
        .session.last_will.topic = NULL,           // 不使用遗嘱消息
        .session.last_will.msg = NULL,
        .session.last_will.msg_len = 0,
        .session.last_will.qos = 0,
        .session.last_will.retain = false,
    };

    mqtt_client = esp_mqtt_client_init(&mqtt_cfg);
    if (mqtt_client == NULL) {
        ESP_LOGE(TAG, "Failed to initialize MQTT client");
        return;
    }

    esp_mqtt_client_register_event(mqtt_client, ESP_EVENT_ANY_ID, mqtt_event_handler, NULL);
    ESP_LOGI(TAG, "MQTT client initialized");

    // 调试：显示最终的MQTT配置
    ESP_LOGI(TAG, "📋 最终MQTT配置:");
    ESP_LOGI(TAG, "  服务器: %s", config.mqtt_broker);
    ESP_LOGI(TAG, "  端口: %d", config.mqtt_port);
    ESP_LOGI(TAG, "  客户端ID: %s", config.sn);
    ESP_LOGI(TAG, "  用户名: %s", config.sn);
    ESP_LOGI(TAG, "  密码: %s (长度: %d)", mqpwd, strlen(mqpwd));

    printf("MQTT client initialized, broker: %s:%d\n", config.mqtt_broker, config.mqtt_port);
    printf("MQTT client ID: %s\n", config.sn);
    printf("MQTT username: %s\n", config.sn);
    printf("MQTT password: %s\n", mqpwd);
}

// WiFi省电模式设置（由WiFi管理器调用）
static void set_wifi_power_save(void)
{
    // 设置WiFi省电模式
    ESP_LOGI(TAG, "Setting WiFi power save mode...");
    esp_err_t ret = esp_wifi_set_ps(DEFAULT_PS_MODE);
    if (ret == ESP_OK) {
        ESP_LOGI(TAG, "WiFi power save mode set to MIN_MODEM");
        printf("✅ WiFi省电模式已启用\n");
    } else {
        ESP_LOGW(TAG, "Failed to set WiFi power save mode: %s", esp_err_to_name(ret));
    }

    // 设置WiFi监听间隔和信标超时
    esp_wifi_set_inactive_time(WIFI_IF_STA, DEFAULT_BEACON_TIMEOUT);
}

// IP地址显示和MQTT发布任务
void ip_display_task(void *pvParameters)
{
    uint32_t loop_count = 0;

    ESP_LOGI(TAG, "IP display and MQTT task started");
    printf("=== IP Display and MQTT Task Started ===\n");

    while (1) {
        loop_count++;

        printf("\n=== Loop %lu ===\n", loop_count);

        // 显示连接状态
        printf("WiFi: %s, MQTT: %s\n",
               wifi_connected ? "Connected" : "Disconnected",
               mqtt_connected ? "Connected" : "Disconnected");

        if (current_ip.addr != 0) {
            printf("Current IP: " IPSTR "\n", IP2STR(&current_ip));

            // IP地址显示 (不发布到MQTT)
        } else {
            printf("No IP address assigned\n");
        }

        // 每5次循环输出系统状态并发布到MQTT
        if (loop_count % 5 == 0) {
            uint32_t free_heap = esp_get_free_heap_size();
            printf("Free heap: %lu bytes\n", free_heap);
            printf("Auto light sleep is active\n");

            // 系统状态显示 (不发布到MQTT)
        }

        printf("Waiting 5 seconds...\n");
        vTaskDelay(5000 / portTICK_PERIOD_MS);
    }
}

void app_main(void)
{
    printf("ESP32-C3 WiFi Power Save Example\n");

    
    // 初始化NVS配置模块
    esp_err_t ret = nvs_config_init();
    if (ret != ESP_OK) {
        ESP_LOGE(TAG, "Failed to initialize NVS: %s", esp_err_to_name(ret));
        return;
    }

    // 配置管理器已在nvs_config_init中初始化

    // 加载配置
    ret = load_config();
    if (ret != ESP_OK) {
        ESP_LOGE(TAG, "Failed to load config: %s", esp_err_to_name(ret));
        return;
    }

    // 初始化电机控制
    ret = motor_control_init();
    if (ret != ESP_OK) {
        ESP_LOGE(TAG, "Failed to initialize motor control: %s", esp_err_to_name(ret));
        return;
    }

    // 初始化LED控制
    ret = led_control_init();
    if (ret != ESP_OK) {
        ESP_LOGE(TAG, "Failed to initialize LED control: %s", esp_err_to_name(ret));
        return;
    }

    // 创建LED控制任务 - 提高优先级确保稳定运行
    xTaskCreate(led_control_task, "led_task", 2048, NULL, 6, NULL);
    ESP_LOGI(TAG, "LED控制任务已创建");

    // 设备启动后LED常亮
    led_set_on();

    // 打印当前配置
    print_config();

    // 单独输出关键信息用于调试
    printf("\n=== 调试信息 ===\n");
    printf("设备MAC: %s\n", macStr);
    printf("设备SN: %s\n", sn);
    printf("SN长度: %d\n", strlen(sn));
    printf("WiFi SSID: '%s' (长度: %d)\n", config.stassid, strlen(config.stassid));
    printf("WiFi密码: %s\n", strlen(config.stapsw) > 0 ? "已设置" : "未设置");
    printf("配网标志: %d\n", config_flag);
    printf("魔数: 0x%02X\n", config.magic);
    printf("===============\n");

#if CONFIG_PM_ENABLE
    // 配置动态频率调节和自动轻度睡眠
    esp_pm_config_t pm_config = {
        .max_freq_mhz = MAX_CPU_FREQ_MHZ,
        .min_freq_mhz = MIN_CPU_FREQ_MHZ,
#if CONFIG_FREERTOS_USE_TICKLESS_IDLE
        .light_sleep_enable = false
#endif
    };
    ESP_ERROR_CHECK(esp_pm_configure(&pm_config));
    ESP_LOGI(TAG, "Power management configured: %d-%d MHz, light sleep: %s", 
             MIN_CPU_FREQ_MHZ, MAX_CPU_FREQ_MHZ,
#if CONFIG_FREERTOS_USE_TICKLESS_IDLE
             "enabled"
#else
             "disabled"
#endif
    );
    printf("Auto light sleep enabled!\n");
#else
    ESP_LOGW(TAG, "Power management not enabled in configuration");
    printf("Power management not enabled in configuration\n");
#endif

    // 提前创建电机控制任务，确保配网阶段按键也能响应
    xTaskCreate(motor_control_task, "motor_task", 4096, NULL, 5, NULL);
    ESP_LOGI(TAG, "电机控制任务已创建 (配网前)");

    // 检查配网状态并处理WiFi连接
    printf("检查WiFi配网状态...\n");

    // 进入配网模式，LED改为快闪
    if (!is_wifi_configured()) {
        ESP_LOGI(TAG, "💡 进入配网模式，LED快闪");
        led_set_fast_blink();
    }

    wait_key();  // 这个函数会处理配网逻辑

    // 如果配网成功，继续初始化其他功能
    if (is_wifi_configured() && get_wifi_state() == STATE_ONLINE) {
        printf("✅ WiFi配网完成，继续初始化系统...\n");
        printf("🔋 低功耗模式已在WiFi连接成功时自动启用\n");

        // LED控制由WiFi事件处理，此处不重复设置

    } else {
        printf("❌ WiFi配网失败，系统将重启\n");
        vTaskDelay(3000 / portTICK_PERIOD_MS);
        esp_restart();
        return;
    }

    // WiFi事件处理已由wifi_manager.c管理，无需重复注册

    // 初始化MQTT客户端
    mqtt_app_start();

    // 检查WiFi是否已经连接
    wifi_ap_record_t ap_info;
    esp_err_t wifi_status = esp_wifi_sta_get_ap_info(&ap_info);
    if (wifi_status == ESP_OK) {
        ESP_LOGI(TAG, "🔍 检测到WiFi已连接: %s", ap_info.ssid);
        wifi_connected = true;
        if (mqtt_client != NULL) {
            ESP_LOGI(TAG, "🚀 立即启动MQTT客户端...");
            esp_err_t ret = esp_mqtt_client_start(mqtt_client);
            if (ret == ESP_OK) {
                ESP_LOGI(TAG, "✅ MQTT客户端启动成功");
            } else {
                ESP_LOGE(TAG, "❌ MQTT客户端启动失败: %s", esp_err_to_name(ret));
            }
        }
    } else {
        ESP_LOGI(TAG, "🔍 WiFi未连接，等待连接后启动MQTT");
        wifi_connected = false;
    }

    // 电机控制任务已在配网前创建

    // 启动电机测试
    ESP_LOGI(TAG, "🚀 启动电机测试...");
    //motor_startup_test();

    // 创建MQTT监控任务
    xTaskCreate(mqtt_wifi_monitor_task, "mqtt_monitor", 4096, NULL, 4, NULL);
    ESP_LOGI(TAG, "MQTT监控任务已创建");

    // 创建IP显示任务（可选）
    // xTaskCreate(ip_display_task, "ip_mqtt_task", 8192, NULL, 3, NULL);

    printf("System initialized. WiFi will connect and enter power save mode.\n");
    printf("WiFi SSID: %s\n", config.stassid);
    printf("MQTT broker: %s:%d\n", config.mqtt_broker, config.mqtt_port);
    printf("Device MAC: %s\n", macStr);
    printf("Device SN: %s\n", sn);
    printf("MQTT主题信息:\n");
    printf("订阅命令主题: controller/%s/device\n", sn);
    printf("设备将接收电机控制和配置更新命令\n");
    
    // 主任务保持运行，定期输出系统状态
    uint32_t main_loop_count = 0;
    while (1) {
        main_loop_count++;

        // 每分钟输出一次系统状态
        if (main_loop_count % 6 == 0) {  // 6 * 10秒 = 60秒
            ESP_LOGI(TAG, "📊 系统状态 (运行时间: %d分钟)", main_loop_count / 6);
            ESP_LOGI(TAG, "  WiFi: %s, MQTT: %s",
                     wifi_connected ? "已连接" : "未连接",
                     mqtt_connected ? "已连接" : "未连接");
            ESP_LOGI(TAG, "  空闲内存: %lu bytes", esp_get_free_heap_size());
            ESP_LOGI(TAG, "  MQTT重连次数: %d", mqtt_reconnect_count);

            // 如果MQTT连接正常，发送心跳
            if (mqtt_connected && mqtt_client != NULL) {
                char heartbeat_topic[128];
                snprintf(heartbeat_topic, sizeof(heartbeat_topic), "controller/%s/heartbeat", config.sn);
                char heartbeat_msg[128];
                snprintf(heartbeat_msg, sizeof(heartbeat_msg),
                        "{\"uptime\":%lu,\"heap\":%lu,\"wifi_rssi\":-50}",
                        (unsigned long)(main_loop_count / 6), esp_get_free_heap_size());
                esp_mqtt_client_publish(mqtt_client, heartbeat_topic, heartbeat_msg, 0, 0, 0);
                ESP_LOGI(TAG, "💓 发送心跳到服务器");
            }
        }

        vTaskDelay(10000 / portTICK_PERIOD_MS);  // 每10秒循环一次
    }
}

// MQTT状态监控任务实现
void mqtt_wifi_monitor_task(void *pvParameters)
{
    ESP_LOGI(TAG, "🔄 MQTT监控任务启动");
    mqtt_loop_running = true;

    while (mqtt_loop_running) {
        uint32_t current_time = xTaskGetTickCount() * portTICK_PERIOD_MS;

        // WiFi状态由ESP-IDF事件系统自动管理，无需手动检查

        // 检查电池状态 (每小时一次)
        if (current_time - last_battery_check_time >= BATTERY_CHECK_INTERVAL_MS) {
            last_battery_check_time = current_time;

            if (first_battery_measure_done) {
                ESP_LOGI(TAG, "🔋 定时电池检测 (每小时)");
                perform_battery_measurement();

                // 发布定时检测的电池状态
                vTaskDelay(pdMS_TO_TICKS(500)); // 等待测量完成
                publish_battery_status();
            }
        }

        // 检查MQTT状态
        if (current_time - last_mqtt_check_time >= MQTT_CHECK_INTERVAL_MS) {
            last_mqtt_check_time = current_time;

            if (wifi_connected && !mqtt_connected && mqtt_client != NULL) {
                mqtt_reconnect_count++;

                if (mqtt_reconnect_count <= MQTT_RECONNECT_MAX_RETRY) {
                    ESP_LOGW(TAG, "� MQTT未连接但WiFi正常，尝试重连MQTT... (%d/%d)",
                             mqtt_reconnect_count, MQTT_RECONNECT_MAX_RETRY);
                    esp_mqtt_client_reconnect(mqtt_client);
                } else if (mqtt_reconnect_count == MQTT_RECONNECT_MAX_RETRY + 1) {
                    ESP_LOGE(TAG, "❌ MQTT重连次数超限，停止自动重连");
                    ESP_LOGE(TAG, "🔧 请检查MQTT服务器状态和网络连接");
                }
            } else if (mqtt_connected) {
                // 重置重连计数器
                if (mqtt_reconnect_count > 0) {
                    ESP_LOGI(TAG, "✅ MQTT连接恢复，重置重连计数器");
                    mqtt_reconnect_count = 0;
                }

                // 只在第一次或每分钟输出一次心跳信息
                static uint32_t last_heartbeat_log = 0;
                if (current_time - last_heartbeat_log >= 60000) {  // 每分钟一次
                    ESP_LOGI(TAG, "💓 MQTT连接正常");
                    last_heartbeat_log = current_time;
                }
            } else if (!wifi_connected) {
                // WiFi未连接时不检查MQTT
                ESP_LOGD(TAG, "📶 WiFi未连接，跳过MQTT检查");
            }
        }

        // 每2秒循环一次，减少CPU占用
        vTaskDelay(pdMS_TO_TICKS(2000));
    }

    ESP_LOGI(TAG, "🔄 MQTT监控任务结束");
    vTaskDelete(NULL);
}

// 发布电池状态到MQTT
static void publish_battery_status(void)
{
    if (!mqtt_connected || mqtt_client == NULL) {
        ESP_LOGW(TAG, "❌ MQTT未连接，无法上传电池数据");
        return;
    }

    ESP_LOGI(TAG, "📡 开始准备电池数据上传...");

    // 构建MQTT主题: controller/battery/[SN]
    char battery_topic[128];
    snprintf(battery_topic, sizeof(battery_topic), "controller/battery/%s", config.sn);

    // 构建简化的电池状态JSON - 只包含voltage和percentage
    cJSON *battery_json = cJSON_CreateObject();
    cJSON_AddNumberToObject(battery_json, "voltage", round(battery_voltage * 1000) / 1000.0); // 保留3位小数
    cJSON_AddNumberToObject(battery_json, "percentage", battery_percentage);

    // 序列化JSON
    char *json_string = cJSON_Print(battery_json);
    if (json_string == NULL) {
        ESP_LOGE(TAG, "❌ JSON序列化失败");
        cJSON_Delete(battery_json);
        return;
    }

    // 输出调试信息
    ESP_LOGI(TAG, "📡 主题: %s", battery_topic);
    ESP_LOGI(TAG, "📋 数据: %s", json_string);

    // 发布电池状态（使用retain标志保持最新状态）
    int msg_id = esp_mqtt_client_publish(mqtt_client, battery_topic, json_string, 0, 1, 1);

    if (msg_id >= 0) {
        ESP_LOGI(TAG, "✅ 电池数据上传成功！(msg_id=%d)", msg_id);
    } else {
        ESP_LOGE(TAG, "❌ 电池数据上传失败");
    }

    // 清理内存
    free(json_string);
    cJSON_Delete(battery_json);
}

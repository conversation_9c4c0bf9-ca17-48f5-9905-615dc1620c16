Command: cmake -G Ninja -DPYTHON_DEPS_CHECKED=1 -DPYTHON=C:\Users\<USER>\.espressif\python_env\idf5.5_py3.11_env\Scripts\python.exe -DESP_PLATFORM=1 -DSDKCONFIG='c:\Users\<USER>\wifi\sdkconfig' -DCCACHE_ENABLE=1 C:\Users\<USER>\wifi
CMake Error:
  Running

   'C:/Users/<USER>/.espressif/tools/ninja/1.12.1/ninja.exe' '-C' 'C:/Users/<USER>/wifi/build' '-t' 'recompact'

  failed with:

   ninja: error: failed recompaction: Permission denied



CMake Error:
  Running

   'C:/Users/<USER>/.espressif/tools/ninja/1.12.1/ninja.exe' '-C' 'C:/Users/<USER>/wifi/build' '-t' 'restat' 'build.ninja'

  failed with:

   ninja: error: failed recompaction: Permission denied



CMake Generate step failed.  Build files cannot be regenerated correctly.

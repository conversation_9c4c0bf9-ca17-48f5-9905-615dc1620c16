/*
 * BLE WiFi Configuration Module Implementation
 * 完整的蓝牙WiFi配网模块实现 - 保留所有功能和数据处理
 */

#include "ble_wifi_config_full.h"
#include <stdio.h>
#include <stdlib.h>
#include <string.h>
#include "esp_log.h"
#include "esp_wifi.h"
#include "esp_mac.h"
#include "nvs_config.h"
#include "led_control.h"
#include "freertos/FreeRTOS.h"
#include "freertos/task.h"
#include "freertos/timers.h"

static const char *TAG = "ble_wifi_config_full";

// 全局变量定义
ble_wifi_config_context_t ble_config_ctx = {0};
bool ble_config_active = false;
bool ble_config_success = false;
uint32_t ble_config_start_time = 0;

// 内部变量
static TimerHandle_t timeout_timer = NULL;



// UUID转换辅助函数
static void uuid_str_to_128(const char* uuid_str, uint8_t* uuid_128) {
    // 将UUID字符串转换为128位UUID
    // 格式: "6E400001-B5A3-F393-E0A9-E50E24DCCA9E"

    printf("🔍 转换UUID: %s\n", uuid_str);

    // 移除连字符并转换为字节数组
    char uuid_no_dash[33];
    int j = 0;
    for (int i = 0; uuid_str[i] != '\0' && j < 32; i++) {
        if (uuid_str[i] != '-') {
            uuid_no_dash[j++] = uuid_str[i];
        }
    }
    uuid_no_dash[32] = '\0';

    printf("🔍 去除连字符: %s\n", uuid_no_dash);

    // 按照标准UUID格式转换，不反转字节序
    // UUID格式：XXXXXXXX-XXXX-XXXX-XXXX-XXXXXXXXXXXX
    // 转换为16字节数组，按照从左到右的顺序
    for (int i = 0; i < 16; i++) {
        char hex_byte[3] = {uuid_no_dash[i*2], uuid_no_dash[i*2+1], '\0'};
        uuid_128[i] = (uint8_t)strtol(hex_byte, NULL, 16);
    }

    printf("🔍 转换结果: ");
    for (int i = 0; i < 16; i++) {
        printf("%02X ", uuid_128[i]);
    }
    printf("\n");
}

/**
 * @brief 计算CRC16校验和 (与Arduino版本保持一致)
 */
uint16_t ble_calculate_crc16(const uint8_t* data, size_t length) {
    uint16_t crc = 0xFFFF;
    
    for (size_t i = 0; i < length; i++) {
        crc ^= (uint16_t)data[i];
        for (int j = 0; j < 8; j++) {
            if (crc & 0x0001) {
                crc = (crc >> 1) ^ 0xA001;
            } else {
                crc >>= 1;
            }
        }
    }
    
    return crc;
}

/**
 * @brief 超时定时器回调函数
 */
static void ble_config_timeout_callback(TimerHandle_t xTimer) {
    ESP_LOGW(TAG, "BLE config timeout after %d ms", BLE_CONFIG_TIMEOUT_MS);
    ble_config_ctx.state = BLE_CONFIG_STATE_TIMEOUT;
    ble_config_active = false;
    
    printf("⏰ 蓝牙配网超时\n");
    
    // 停止蓝牙配网
    ble_wifi_config_stop();
}

/**
 * @brief GAP事件处理函数
 */
static void ble_gap_event_handler(esp_gap_ble_cb_event_t event, esp_ble_gap_cb_param_t *param) {
    switch (event) {
    case ESP_GAP_BLE_ADV_DATA_SET_COMPLETE_EVT:
        ESP_LOGI(TAG, "Advertisement data set complete");
        esp_ble_gap_start_advertising(&(esp_ble_adv_params_t){
            .adv_int_min = 0x20,
            .adv_int_max = 0x40,
            .adv_type = ADV_TYPE_IND,
            .own_addr_type = BLE_ADDR_TYPE_PUBLIC,
            .channel_map = ADV_CHNL_ALL,
            .adv_filter_policy = ADV_FILTER_ALLOW_SCAN_ANY_CON_ANY,
        });
        break;
        
    case ESP_GAP_BLE_ADV_START_COMPLETE_EVT:
        if (param->adv_start_cmpl.status == ESP_BT_STATUS_SUCCESS) {
            ESP_LOGI(TAG, "Advertisement started successfully");
            ble_config_ctx.state = BLE_CONFIG_STATE_ADVERTISING;
            printf("📡 BLE广播已启动\n");
        } else {
            ESP_LOGE(TAG, "Advertisement start failed");
            ble_config_ctx.state = BLE_CONFIG_STATE_FAILED;
        }
        break;
        
    case ESP_GAP_BLE_ADV_STOP_COMPLETE_EVT:
        ESP_LOGI(TAG, "Advertisement stopped");
        break;
        
    default:
        break;
    }
}

/**
 * @brief GATTS事件处理函数
 */
static void ble_gatts_event_handler(esp_gatts_cb_event_t event, esp_gatt_if_t gatts_if, esp_ble_gatts_cb_param_t *param) {
    // 监控所有GATTS事件
    const char* event_name = "未知";
    switch(event) {
        case ESP_GATTS_REG_EVT: event_name = "REG"; break;
        case ESP_GATTS_READ_EVT: event_name = "READ"; break;
        case ESP_GATTS_WRITE_EVT: event_name = "WRITE"; break;
        case ESP_GATTS_EXEC_WRITE_EVT: event_name = "EXEC_WRITE"; break;
        case ESP_GATTS_MTU_EVT: event_name = "MTU"; break;
        case ESP_GATTS_CONF_EVT: event_name = "CONF"; break;
        case ESP_GATTS_UNREG_EVT: event_name = "UNREG"; break;
        case ESP_GATTS_CREATE_EVT: event_name = "CREATE"; break;
        case ESP_GATTS_ADD_INCL_SRVC_EVT: event_name = "ADD_INCL_SRVC"; break;
        case ESP_GATTS_ADD_CHAR_EVT: event_name = "ADD_CHAR"; break;
        case ESP_GATTS_ADD_CHAR_DESCR_EVT: event_name = "ADD_CHAR_DESCR"; break;
        case ESP_GATTS_DELETE_EVT: event_name = "DELETE"; break;
        case ESP_GATTS_START_EVT: event_name = "START"; break;
        case ESP_GATTS_STOP_EVT: event_name = "STOP"; break;
        case ESP_GATTS_CONNECT_EVT: event_name = "CONNECT"; break;
        case ESP_GATTS_DISCONNECT_EVT: event_name = "DISCONNECT"; break;
        case ESP_GATTS_OPEN_EVT: event_name = "OPEN"; break;
        case ESP_GATTS_CANCEL_OPEN_EVT: event_name = "CANCEL_OPEN"; break;
        case ESP_GATTS_CLOSE_EVT: event_name = "CLOSE"; break;
        case ESP_GATTS_LISTEN_EVT: event_name = "LISTEN"; break;
        case ESP_GATTS_CONGEST_EVT: event_name = "CONGEST"; break;
        default: event_name = "未知"; break;
    }
    printf("🔔 GATTS事件: %d (%s) (接口: %d)\n", event, event_name, gatts_if);

    switch (event) {
    case ESP_GATTS_REG_EVT:
        ESP_LOGI(TAG, "GATTS register complete");
        ble_config_ctx.gatts_if = gatts_if;
        
        // 设置设备名称 - 直接使用sn
        printf("🔍 调试BLE设备名称设置:\n");
        printf("   sn = '%s' (长度: %d)\n", sn, strlen(sn));
        printf("   macStr = '%s'\n", macStr);

        // 直接使用sn作为设备名称
        esp_ble_gap_set_device_name(sn);
        ESP_LOGI(TAG, "BLE device name set to: %s", sn);
        printf("✅ BLE设备名称已设置为: %s\n", sn);

        // 创建GATT服务 - 使用直接定义的UUID
        esp_gatt_srvc_id_t service_id;
        service_id.is_primary = true;
        service_id.id.inst_id = 0x00;
        service_id.id.uuid.len = ESP_UUID_LEN_128;

        // 直接定义服务UUID字节数组 (6E400001-B5A3-F393-E0A9-E50E24DCCA9E)
        uint8_t service_uuid128[16] = {
            0x9e, 0xca, 0xdc, 0x24, 0x0e, 0xe5, 0xa9, 0xe0,
            0x93, 0xf3, 0xa3, 0xb5, 0x01, 0x00, 0x40, 0x6e
        };
        memcpy(service_id.id.uuid.uuid.uuid128, service_uuid128, 16);

        esp_ble_gatts_create_service(gatts_if, &service_id, 8); // 增加句柄数量确保足够
        printf("🔧 创建GATT服务: %s (分配8个句柄)\n", BLE_SERVICE_UUID);
        break;

    case ESP_GATTS_CREATE_EVT:
        ESP_LOGI(TAG, "Service created, status: %d, service_handle: %d", param->create.status, param->create.service_handle);
        ble_config_ctx.service_handle = param->create.service_handle;

        // 启动服务
        esp_ble_gatts_start_service(param->create.service_handle);

        // 添加命令特征 (只写入，与Arduino版本一致)
        esp_bt_uuid_t cmd_char_uuid;
        cmd_char_uuid.len = ESP_UUID_LEN_128;

        // 直接定义命令特征UUID字节数组 (6E400002-B5A3-F393-E0A9-E50E24DCCA9E)
        uint8_t cmd_uuid128[16] = {
            0x9e, 0xca, 0xdc, 0x24, 0x0e, 0xe5, 0xa9, 0xe0,
            0x93, 0xf3, 0xa3, 0xb5, 0x02, 0x00, 0x40, 0x6e
        };
        memcpy(cmd_char_uuid.uuid.uuid128, cmd_uuid128, 16);

        // 调试输出UUID
        printf("🔍 命令特征UUID (write): ");
        for (int i = 0; i < 16; i++) {
            printf("%02X ", cmd_char_uuid.uuid.uuid128[i]);
        }
        printf("\n");

        // 设置最大兼容性的特征属性
        esp_gatt_char_prop_t cmd_property = ESP_GATT_CHAR_PROP_BIT_WRITE |
                                           ESP_GATT_CHAR_PROP_BIT_WRITE_NR |
                                           ESP_GATT_CHAR_PROP_BIT_READ;
        esp_gatt_perm_t cmd_perm = ESP_GATT_PERM_READ | ESP_GATT_PERM_WRITE;

        // 简化特征配置，使用默认值
        esp_err_t ret = esp_ble_gatts_add_char(ble_config_ctx.service_handle, &cmd_char_uuid, cmd_perm, cmd_property, NULL, NULL);
        printf("🔧 添加命令特征到服务句柄 %d: %s (结果: %s)\n", ble_config_ctx.service_handle, BLE_CMD_CHAR_UUID, esp_err_to_name(ret));
        break;

    case ESP_GATTS_ADD_CHAR_EVT:
        ESP_LOGI(TAG, "Characteristic added, status: %d, attr_handle: %d", param->add_char.status, param->add_char.attr_handle);
        printf("📝 特征添加完成，句柄: %d，状态: %d\n", param->add_char.attr_handle, param->add_char.status);

        if (ble_config_ctx.cmd_char_handle == 0) {
            // 第一个特征是命令特征
            ble_config_ctx.cmd_char_handle = param->add_char.attr_handle;
            printf("✅ 命令特征句柄已保存: %d\n", ble_config_ctx.cmd_char_handle);

            // 添加数据特征 (读取和通知，与Arduino版本一致)
            esp_bt_uuid_t data_char_uuid;
            data_char_uuid.len = ESP_UUID_LEN_128;

            // 直接定义数据特征UUID字节数组 (6E400003-B5A3-F393-E0A9-E50E24DCCA9E)
            uint8_t data_uuid128[16] = {
                0x9e, 0xca, 0xdc, 0x24, 0x0e, 0xe5, 0xa9, 0xe0,
                0x93, 0xf3, 0xa3, 0xb5, 0x03, 0x00, 0x40, 0x6e
            };
            memcpy(data_char_uuid.uuid.uuid128, data_uuid128, 16);

            // 调试输出UUID
            printf("🔍 数据特征UUID (notify): ");
            for (int i = 0; i < 16; i++) {
                printf("%02X ", data_char_uuid.uuid.uuid128[i]);
            }
            printf("\n");

            // 与Arduino版本一致：READ和NOTIFY属性
            esp_gatt_char_prop_t data_property = ESP_GATT_CHAR_PROP_BIT_READ | ESP_GATT_CHAR_PROP_BIT_NOTIFY;
            esp_gatt_perm_t data_perm = ESP_GATT_PERM_READ;

            // 简化数据特征配置
            esp_err_t ret = esp_ble_gatts_add_char(ble_config_ctx.service_handle, &data_char_uuid, data_perm, data_property, NULL, NULL);
            printf("🔧 添加数据特征到服务句柄 %d: %s (结果: %s)\n", ble_config_ctx.service_handle, BLE_DATA_CHAR_UUID, esp_err_to_name(ret));
        } else {
            // 第二个特征是数据特征
            ble_config_ctx.data_char_handle = param->add_char.attr_handle;
            printf("✅ 数据特征句柄已保存: %d\n", ble_config_ctx.data_char_handle);

            // 为数据特征添加BLE2902描述符 (Client Characteristic Configuration)
            // 这对于通知功能是必需的，与Arduino版本的addDescriptor(new BLE2902())对应
            esp_bt_uuid_t desc_uuid;
            desc_uuid.len = ESP_UUID_LEN_16;
            desc_uuid.uuid.uuid16 = ESP_GATT_UUID_CHAR_CLIENT_CONFIG;

            // 创建描述符值结构
            static uint8_t desc_value_data[2] = {0x00, 0x00}; // 默认禁用通知
            esp_attr_value_t desc_value = {
                .attr_max_len = 2,
                .attr_len = 2,
                .attr_value = desc_value_data,
            };
            esp_gatt_perm_t desc_perm = ESP_GATT_PERM_READ | ESP_GATT_PERM_WRITE;

            esp_err_t ret = esp_ble_gatts_add_char_descr(ble_config_ctx.service_handle, &desc_uuid, desc_perm, &desc_value, NULL);
            printf("🔧 添加BLE2902描述符 (结果: %s)\n", esp_err_to_name(ret));
        }
        break;

    case ESP_GATTS_ADD_CHAR_DESCR_EVT:
        ESP_LOGI(TAG, "Descriptor added, status: %d, attr_handle: %d", param->add_char_descr.status, param->add_char_descr.attr_handle);

        // 描述符添加完成，现在配置广播数据
        uint8_t adv_service_uuid128[16];
        uuid_str_to_128(BLE_SERVICE_UUID, adv_service_uuid128);

        esp_ble_adv_data_t adv_data = {
            .set_scan_rsp = false,
            .include_name = true,
            .include_txpower = true,
            .min_interval = 0x0006,
            .max_interval = 0x0010,
            .appearance = 0x00,
            .manufacturer_len = 0,
            .p_manufacturer_data = NULL,
            .service_data_len = 0,
            .p_service_data = NULL,
            .service_uuid_len = 16,
            .p_service_uuid = adv_service_uuid128,
            .flag = (ESP_BLE_ADV_FLAG_GEN_DISC | ESP_BLE_ADV_FLAG_BREDR_NOT_SPT),
        };
        esp_ble_gap_config_adv_data(&adv_data);
        printf("✅ GATT服务、特征和描述符创建完成\n");

        // 输出完整的句柄映射
        printf("\n📋 BLE句柄映射表:\n");
        printf("   服务句柄: %d\n", ble_config_ctx.service_handle);
        printf("   命令特征句柄: %d (UUID: %s)\n", ble_config_ctx.cmd_char_handle, BLE_CMD_CHAR_UUID);
        printf("   数据特征句柄: %d (UUID: %s)\n", ble_config_ctx.data_char_handle, BLE_DATA_CHAR_UUID);
        printf("   预期句柄范围: %d - %d\n", ble_config_ctx.service_handle, ble_config_ctx.service_handle + 10);
        printf("========================\n");
        break;

    case ESP_GATTS_CONNECT_EVT:
        ESP_LOGI(TAG, "BLE client connected");
        printf("📱 设备已连接!\n");
        printf("   连接ID: %d\n", param->connect.conn_id);
        printf("   远程地址: %02X:%02X:%02X:%02X:%02X:%02X\n",
               param->connect.remote_bda[0], param->connect.remote_bda[1],
               param->connect.remote_bda[2], param->connect.remote_bda[3],
               param->connect.remote_bda[4], param->connect.remote_bda[5]);
        printf("   GATTS接口: %d\n", gatts_if);
        printf("   命令特征句柄: %d\n", ble_config_ctx.cmd_char_handle);
        printf("   数据特征句柄: %d\n", ble_config_ctx.data_char_handle);

        ble_config_ctx.device_connected = true;
        ble_config_ctx.conn_id = param->connect.conn_id;
        ble_config_ctx.gatts_if = gatts_if;
        ble_config_ctx.state = BLE_CONFIG_STATE_CONNECTED;

        // 请求更大的MTU以支持更长的数据包
        esp_err_t mtu_ret = esp_ble_gatt_set_local_mtu(512);
        printf("🔧 设置本地MTU为512: %s\n", esp_err_to_name(mtu_ret));
        break;
        
    case ESP_GATTS_DISCONNECT_EVT:
        ESP_LOGI(TAG, "BLE client disconnected");
        printf("📱 设备已断开连接!\n");
        ble_config_ctx.device_connected = false;
        
        // 如果配网未成功，重新开始广播
        if (ble_config_ctx.state != BLE_CONFIG_STATE_SUCCESS) {
            ble_config_ctx.state = BLE_CONFIG_STATE_ADVERTISING;
            esp_ble_gap_start_advertising(&(esp_ble_adv_params_t){
                .adv_int_min = 0x20,
                .adv_int_max = 0x40,
                .adv_type = ADV_TYPE_IND,
                .own_addr_type = BLE_ADDR_TYPE_PUBLIC,
                .channel_map = ADV_CHNL_ALL,
                .adv_filter_policy = ADV_FILTER_ALLOW_SCAN_ANY_CON_ANY,
            });
        }
        break;
        
    case ESP_GATTS_WRITE_EVT:
        ESP_LOGI(TAG, "GATTS write event, handle: %d, len: %d", param->write.handle, param->write.len);
        printf("\n=== BLE写入事件详情 ===\n");
        printf("📝 收到BLE写入事件:\n");
        printf("   ⚠️ 重要：如果数据长度不是8字节，说明数据被截断！\n");
        printf("   写入句柄: %d\n", param->write.handle);
        printf("   命令特征句柄: %d\n", ble_config_ctx.cmd_char_handle);
        printf("   数据特征句柄: %d\n", ble_config_ctx.data_char_handle);
        printf("   服务句柄: %d\n", ble_config_ctx.service_handle);
        printf("   连接ID: %d\n", param->write.conn_id);
        printf("   传输ID: %lu\n", (unsigned long)param->write.trans_id);
        printf("   偏移: %d\n", param->write.offset);
        printf("   需要响应: %s\n", param->write.need_rsp ? "是" : "否");
        printf("   是否准备: %s\n", param->write.is_prep ? "是" : "否");
        // 检查句柄匹配情况
        bool is_cmd_char = (param->write.handle == ble_config_ctx.cmd_char_handle);
        bool is_data_char = (param->write.handle == ble_config_ctx.data_char_handle);
        bool is_related_handle = (param->write.handle >= ble_config_ctx.service_handle &&
                                 param->write.handle <= ble_config_ctx.service_handle + 10);

        printf("   句柄匹配: %s\n",
               is_cmd_char ? "✅命令特征" :
               is_data_char ? "✅数据特征" :
               is_related_handle ? "🔍相关句柄(可能是描述符)" :
               "❌未知特征");

        if (is_related_handle && !is_cmd_char && !is_data_char) {
            printf("   🔍 句柄%d可能是服务相关的描述符或其他特征\n", param->write.handle);
            printf("   📝 尝试按命令特征处理此写入\n");
        }
        printf("   数据长度: %d 字节\n", param->write.len);

        // 检查数据长度异常
        if (param->write.len == 2) {
            printf("   🚨 警告：只收到2字节数据，这不正常！\n");
            printf("   🔍 可能原因：\n");
            printf("      1. 小程序实际发送的数据被截断\n");
            printf("      2. BLE特征值配置问题\n");
            printf("      3. MTU限制问题\n");
        } else if (param->write.len == 8) {
            printf("   ✅ 收到8字节数据，这是正确的包头长度\n");
        } else {
            printf("   ⚠️ 收到%d字节数据，长度异常\n", param->write.len);
        }

        printf("   原始数据 (十六进制): ");
        for (int i = 0; i < param->write.len; i++) {
            printf("%02X ", param->write.value[i]);
            if ((i + 1) % 16 == 0 && i + 1 < param->write.len) {
                printf("\n                           ");
            }
        }
        printf("\n");

        // 如果数据包含可打印字符，也显示ASCII
        printf("   原始数据 (ASCII): ");
        for (int i = 0; i < param->write.len; i++) {
            if (param->write.value[i] >= 32 && param->write.value[i] <= 126) {
                printf("%c", param->write.value[i]);
            } else {
                printf(".");
            }
        }
        printf("\n");

        // 详细分析数据包类型
        if (param->write.len >= 2) {
            uint8_t type = param->write.value[0];
            uint8_t sub_type = param->write.value[1];

            printf("\n📊 数据包详细分析:\n");
            printf("   消息类型: 0x%02X (%s)\n", type,
                   type == 0x01 ? "命令(MSG_COMMAND)" :
                   type == 0x02 ? "响应(MSG_RESPONSE)" :
                   type == 0x03 ? "数据(MSG_DATA)" :
                   type == 0x04 ? "确认(MSG_CONFIRM)" : "未知类型");
            printf("   子类型: 0x%02X", sub_type);

            if (type == 0x01) { // MSG_COMMAND
                printf(" (%s)\n",
                       sub_type == 0x00 ? "未知命令" :
                       sub_type == 0x01 ? "获取设备信息" :
                       sub_type == 0x02 ? "扫描WiFi" :
                       sub_type == 0x03 ? "设置WiFi" :
                       sub_type == 0x04 ? "连接WiFi" :
                       sub_type == 0x05 ? "重启设备" : "其他命令");
            } else if (type == 0x03) { // MSG_DATA
                printf(" (%s)\n",
                       sub_type == 0x01 ? "SSID数据" :
                       sub_type == 0x02 ? "密码数据" :
                       sub_type == 0x03 ? "配置数据" : "其他数据");

                printf("   🔍 这是WiFi配置数据:\n");

                if (param->write.len == 2) {
                    printf("      ❌ 错误: 数据消息只有2字节，缺少实际数据\n");
                } else if (param->write.len >= 8) {
                    printf("      ✅ 数据长度正常，包含完整包头\n");

                    // 解析包头信息
                    if (param->write.len >= 8) {
                        uint8_t flags = param->write.value[2];
                        uint8_t seq = param->write.value[3];
                        uint16_t data_len = param->write.value[4] | (param->write.value[5] << 8);
                        uint16_t crc = param->write.value[6] | (param->write.value[7] << 8);

                        printf("      标志位: 0x%02X\n", flags);
                        printf("      序列号: %d\n", seq);
                        printf("      数据长度: %d\n", data_len);
                        printf("      CRC校验: 0x%04X\n", crc);

                        if (param->write.len >= 8 + data_len) {
                            printf("      实际数据: ");
                            for (int i = 8; i < 8 + data_len && i < param->write.len; i++) {
                                if (param->write.value[i] >= 32 && param->write.value[i] <= 126) {
                                    printf("%c", param->write.value[i]);
                                } else {
                                    printf("\\x%02X", param->write.value[i]);
                                }
                            }
                            printf("\n");
                        }
                    }
                } else {
                    printf("      ⚠️ 数据长度异常: %d字节 (期望至少8字节)\n", param->write.len);
                }
            } else {
                printf("\n");
            }
        } else {
            printf("\n⚠️ 数据包太小，无法分析类型\n");
        }

        // 特殊处理：如果收到2字节数据，尝试强制按8字节处理
        if (param->write.len == 2 && param->write.value[0] == 0x01) {
            printf("\n🔧 检测到2字节数据，尝试特殊处理...\n");
            printf("   原始2字节: %02X %02X\n", param->write.value[0], param->write.value[1]);

            // 构造一个模拟的8字节数据包用于测试
            uint8_t test_packet[8] = {0x01, 0x01, 0x00, 0x00, 0x00, 0x00, 0xFF, 0xFF};
            printf("   使用模拟8字节数据包进行测试: ");
            for (int i = 0; i < 8; i++) {
                printf("%02X ", test_packet[i]);
            }
            printf("\n");

            printf("🚀 开始处理模拟数据包...\n");
            printf("========================\n");
            ble_process_received_packet(test_packet, 8);
            printf("========================\n");
            printf("✅ 模拟数据包处理完成\n\n");
        }

        // 处理写入的数据 - 接受命令特征或相关句柄的写入
        if (is_cmd_char || is_data_char || is_related_handle) {
            printf("\n🚀 开始处理原始数据包...\n");
            printf("========================\n");
            ble_process_received_packet(param->write.value, param->write.len);
            printf("========================\n");
            printf("✅ 原始数据包处理完成\n\n");
        } else {
            printf("⚠️ 忽略未知句柄的写入\n");
        }

        // 如果需要响应，发送写入响应
        if (param->write.need_rsp) {
            esp_ble_gatts_send_response(gatts_if, param->write.conn_id, param->write.trans_id, ESP_GATT_OK, NULL);
            printf("📤 已发送写入响应\n");
        }

        break;
        
    default:
        break;
    }
}

/**
 * @brief 发送数据包 (与Arduino版本保持一致的分片逻辑)
 */
esp_err_t ble_send_packet(uint8_t type, uint8_t sub_type, const uint8_t* data, size_t length, bool more_fragments) {
    if (!ble_config_ctx.device_connected) {
        return ESP_ERR_INVALID_STATE;
    }
    
    // 最大有效负载大小
    const size_t MAX_PAYLOAD = BLE_MAX_PACKET_SIZE - sizeof(ble_packet_header_t);
    
    // 分片发送较大的数据
    size_t offset = 0;
    size_t remaining = length;
    uint8_t current_seq = 0;
    
    while (remaining > 0) {
        // 确定当前分片大小
        size_t chunk_size = (remaining > MAX_PAYLOAD) ? MAX_PAYLOAD : remaining;
        bool is_last_fragment = (remaining <= MAX_PAYLOAD && !more_fragments);
        
        // 准备数据包
        size_t packet_size = sizeof(ble_packet_header_t) + chunk_size;
        uint8_t packet[BLE_MAX_PACKET_SIZE];
        
        // 设置包头 (与Arduino版本一致)
        ble_packet_header_t *header = (ble_packet_header_t*)packet;
        header->type = type;
        header->subType = sub_type;
        header->flags = is_last_fragment ? 0x00 : 0x01; // 0x01表示有更多分片
        header->seq = current_seq++;
        header->length = chunk_size;
        
        // 复制数据
        if (data && chunk_size > 0) {
            memcpy(packet + sizeof(ble_packet_header_t), data + offset, chunk_size);
        }
        
        // 计算CRC (与Arduino版本一致)
        header->crc = ble_calculate_crc16(packet + sizeof(ble_packet_header_t), chunk_size);
        
        // 发送数据
        esp_ble_gatts_send_indicate(ble_config_ctx.gatts_if, ble_config_ctx.conn_id, 
                                   ble_config_ctx.data_char_handle, packet_size, packet, false);
        
        // 更新状态
        offset += chunk_size;
        remaining -= chunk_size;
        
        // 分片之间的延迟，确保接收方有足够时间处理
        vTaskDelay(20 / portTICK_PERIOD_MS);
    }
    
    return ESP_OK;
}

/**
 * @brief 处理接收到的数据包 (与Arduino版本保持一致)
 */
void ble_process_received_packet(const uint8_t* data, size_t data_length) {
    printf("\n🔍 开始处理BLE数据包:\n");
    printf("   数据长度: %d\n", data_length);
    printf("   包头大小: %d\n", sizeof(ble_packet_header_t));
    printf("   原始数据: ");
    for (int i = 0; i < data_length && i < 16; i++) {
        printf("%02X ", data[i]);
    }
    if (data_length > 16) printf("...");
    printf("\n");

    // 检查是否是简化命令格式 (2字节: type + subtype)
    if (data_length == 2) {
        printf("🔍 检测到简化命令格式 (2字节)\n");
        uint8_t type = data[0];
        uint8_t sub_type = data[1];
        printf("   类型: 0x%02X (%s)\n", type,
               type == MSG_COMMAND ? "命令" :
               type == MSG_DATA ? "数据" : "未知");
        printf("   子类型: 0x%02X\n", sub_type);

        // 处理简化格式
        if (type == MSG_COMMAND) {
            printf("➡️ 处理简化命令\n");
            ble_handle_command(sub_type, NULL, 0);
        } else if (type == MSG_DATA) {
            printf("➡️ 处理简化数据 (注意：SSID/密码应该有数据内容)\n");
            printf("   警告：简化数据格式通常不包含实际数据\n");
            ble_handle_data(sub_type, NULL, 0);
        } else {
            printf("❌ 不支持的简化消息类型: 0x%02X\n", type);
            ble_send_error_response("不支持的消息类型");
        }
        return;
    }

    // 检查是否是其他简化格式
    if (data_length < sizeof(ble_packet_header_t) && data_length > 2) {
        printf("⚠️ 检测到异常数据包长度: %d 字节\n", data_length);
        printf("   不是标准的8字节包头，也不是2字节简化格式\n");
        printf("   尝试按简化格式处理前2字节\n");

        if (data_length >= 2) {
            uint8_t type = data[0];
            uint8_t sub_type = data[1];
            printf("   类型: 0x%02X, 子类型: 0x%02X\n", type, sub_type);

            if (type == MSG_COMMAND) {
                ble_handle_command(sub_type, NULL, 0);
            } else if (type == MSG_DATA) {
                ble_handle_data(sub_type, data + 2, data_length - 2);
            }
        }
        return;
    }

    // 检查完整包头格式
    if (data_length < sizeof(ble_packet_header_t)) {
        printf("❌ 数据包太小，期望至少 %d 字节，收到 %d 字节\n",
               sizeof(ble_packet_header_t), data_length);
        return;
    }

    const ble_packet_header_t *header = (const ble_packet_header_t*)data;

    // 直接使用Arduino兼容的字段
    uint16_t payload_length = header->length;
    uint16_t crc = header->crc;

    printf("📦 数据包头信息:\n");
    printf("   类型: 0x%02X\n", header->type);
    printf("   子类型: 0x%02X\n", header->subType);
    printf("   标志: 0x%02X\n", header->flags);
    printf("   序列号: %d\n", header->seq);
    printf("   数据长度: %d\n", payload_length);
    printf("   CRC: 0x%04X\n", crc);
    
    // 验证CRC (针对当前分包的有效载荷)
    const uint8_t* payload = data + sizeof(ble_packet_header_t);
    uint16_t calculated_crc = ble_calculate_crc16(payload, payload_length);
    printf("🔐 分包CRC校验:\n");
    printf("   分包序列号: %d\n", header->seq);
    printf("   分包数据长度: %d\n", payload_length);
    printf("   接收到的CRC: 0x%04X\n", crc);
    printf("   计算的CRC: 0x%04X\n", calculated_crc);
    printf("   分包数据: ");
    for (int i = 0; i < payload_length && i < 16; i++) { // 只显示前16字节
        printf("%02X ", payload[i]);
    }
    if (payload_length > 16) printf("...");
    printf("\n");

    if (calculated_crc != crc) {
        printf("❌ 分包CRC校验失败！\n");
        ble_send_error_response("CRC校验失败");
        return;
    }
    printf("✅ 分包CRC校验通过\n");
    
    // 处理分包重组
    printf("\n📋 分包重组处理:\n");
    printf("   当前分包序列号: %d\n", header->seq);
    printf("   分包标志位: 0x%02X (更多分包: %s)\n", header->flags, (header->flags & 0x01) ? "是" : "否");

    if (header->seq == 0) {
        // 新的数据传输开始
        printf("🆕 开始新的分包传输:\n");
        printf("   消息类型: 0x%02X\n", header->type);
        printf("   子类型: 0x%02X\n", header->subType);
        ble_config_ctx.receive_buffer_len = 0;
        ble_config_ctx.last_packet_type = header->type;
        ble_config_ctx.expecting_more_data = (header->flags & 0x01) != 0;
        printf("   重置接收缓冲区\n");
        printf("   期待更多分包: %s\n", ble_config_ctx.expecting_more_data ? "是" : "否");
    } else {
        // 继续接收分包
        printf("📦 继续接收分包 %d:\n", header->seq);
        if (header->type != ble_config_ctx.last_packet_type) {
            printf("❌ 分包类型不匹配！\n");
            printf("   期待类型: 0x%02X\n", ble_config_ctx.last_packet_type);
            printf("   收到类型: 0x%02X\n", header->type);
            ble_send_error_response("数据包类型不匹配");
            return;
        }
        printf("   分包类型匹配: 0x%02X\n", header->type);
    }
    
    // 分包数据追加到重组缓冲区
    printf("\n📝 分包数据追加:\n");
    printf("   分包序列号: %d\n", header->seq);
    printf("   分包数据长度: %d 字节\n", payload_length);
    printf("   当前重组缓冲区长度: %d 字节\n", ble_config_ctx.receive_buffer_len);
    printf("   追加后总长度: %d 字节\n", ble_config_ctx.receive_buffer_len + payload_length);
    printf("   缓冲区最大容量: %d 字节\n", BLE_RECEIVE_BUFFER_SIZE);

    if (ble_config_ctx.receive_buffer_len + payload_length <= BLE_RECEIVE_BUFFER_SIZE) {
        // 显示分包数据内容
        printf("   分包 %d 数据内容: ", header->seq);
        for (int i = 0; i < payload_length && i < 32; i++) { // 最多显示32字节
            if (payload[i] >= 32 && payload[i] <= 126) {
                printf("%c", payload[i]); // 可打印字符
            } else {
                printf("\\x%02X", payload[i]); // 不可打印字符用十六进制
            }
        }
        if (payload_length > 32) printf("...");
        printf("\n");

        // 追加数据到重组缓冲区
        memcpy(ble_config_ctx.receive_buffer + ble_config_ctx.receive_buffer_len, payload, payload_length);
        ble_config_ctx.receive_buffer_len += payload_length;

        printf("✅ 分包 %d 数据追加成功\n", header->seq);
        printf("   重组缓冲区新长度: %d 字节\n", ble_config_ctx.receive_buffer_len);

        // 显示当前重组缓冲区的内容摘要
        printf("   重组缓冲区内容摘要: ");
        for (int i = 0; i < ble_config_ctx.receive_buffer_len && i < 16; i++) {
            printf("%02X ", ble_config_ctx.receive_buffer[i]);
        }
        if (ble_config_ctx.receive_buffer_len > 16) printf("...");
        printf("\n");
    } else {
        printf("❌ 重组缓冲区溢出！\n");
        printf("   需要空间: %d 字节\n", ble_config_ctx.receive_buffer_len + payload_length);
        printf("   可用空间: %d 字节\n", BLE_RECEIVE_BUFFER_SIZE);
        ble_send_error_response("缓冲区溢出");
        return;
    }

    // 发送分包接收确认
    printf("\n📤 发送分包接收确认:\n");
    printf("   确认分包序列号: %d\n", header->seq);
    uint8_t ack_data = header->seq;
    ble_send_packet(MSG_CONFIRM, CONFIRM_RECEIVED, &ack_data, 1, false);
    printf("✅ 分包 %d 确认已发送\n", header->seq);

    // 检查分包传输是否完成
    bool is_last_fragment = (header->flags & 0x01) == 0;
    printf("\n🏁 检查分包传输状态:\n");
    printf("   当前分包序列号: %d\n", header->seq);
    printf("   分包标志位: 0x%02X\n", header->flags);
    printf("   标志位含义: %s\n", (header->flags & 0x01) ? "还有更多分包" : "这是最后一个分包");
    printf("   是否为最后分包: %s\n", is_last_fragment ? "是" : "否");

    if (is_last_fragment) {
        printf("\n🎉 所有分包接收完成，开始处理完整消息:\n");
        printf("   消息类型: 0x%02X (%s)\n", header->type,
               header->type == MSG_COMMAND ? "命令" :
               header->type == MSG_DATA ? "数据" : "其他");
        printf("   子类型: 0x%02X\n", header->subType);
        printf("   重组后总数据长度: %d 字节\n", ble_config_ctx.receive_buffer_len);

        // 显示完整重组数据的摘要
        printf("   完整数据内容: ");
        for (int i = 0; i < ble_config_ctx.receive_buffer_len && i < 64; i++) {
            if (ble_config_ctx.receive_buffer[i] >= 32 && ble_config_ctx.receive_buffer[i] <= 126) {
                printf("%c", ble_config_ctx.receive_buffer[i]);
            } else {
                printf("\\x%02X", ble_config_ctx.receive_buffer[i]);
            }
        }
        if (ble_config_ctx.receive_buffer_len > 64) printf("...");
        printf("\n");

        // 处理完整消息
        printf("➡️ 转发完整消息到消息处理器\n");
        ble_handle_complete_message(header->type, header->subType, ble_config_ctx.receive_buffer, ble_config_ctx.receive_buffer_len);

        // 重置重组缓冲区
        ble_config_ctx.receive_buffer_len = 0;
        printf("🧹 重组缓冲区已重置\n");

        // 发送传输完成确认
        printf("📤 发送传输完成确认\n");
        ble_send_packet(MSG_CONFIRM, CONFIRM_COMPLETE, NULL, 0, false);
        printf("✅ 传输完成确认已发送\n");
    } else {
        printf("⏳ 等待更多分包...\n");
    }

    printf("🔍 分包 %d 处理完成\n", header->seq);
    printf("==================================================\n");
}

/**
 * @brief 处理完整的消息 (与Arduino版本保持一致)
 */
void ble_handle_complete_message(uint8_t type, uint8_t sub_type, const uint8_t* data, size_t length) {
    printf("🎯 处理完整消息:\n");
    printf("   消息类型: 0x%02X (%s)\n", type,
           type == MSG_COMMAND ? "命令" :
           type == MSG_DATA ? "数据" :
           type == MSG_RESPONSE ? "响应" :
           type == MSG_CONFIRM ? "确认" : "未知");
    printf("   子类型: 0x%02X\n", sub_type);
    printf("   数据长度: %d\n", length);

    switch (type) {
        case MSG_COMMAND:
            printf("➡️ 转发到命令处理器\n");
            ble_handle_command(sub_type, data, length);
            break;
        case MSG_DATA:
            printf("➡️ 转发到数据处理器\n");
            ble_handle_data(sub_type, data, length);
            break;
        default:
            printf("❌ 未知的消息类型: 0x%02X\n", type);
            break;
    }
}

/**
 * @brief 处理命令 (与Arduino版本保持一致)
 */
void ble_handle_command(uint8_t sub_type, const uint8_t* data, size_t length) {
    printf("📱 收到BLE命令: 0x%02X\n", sub_type);

    switch (sub_type) {
        case 0x00:
            printf("📋 处理命令: 未知命令0x00，当作获取设备信息处理\n");
            ble_send_device_info();
            break;
        case CMD_GET_DEVICE_INFO:
            printf("📋 处理命令: 获取设备信息\n");
            ble_send_device_info();
            break;
        case CMD_SCAN_WIFI:
            printf("🔍 处理命令: 扫描WiFi网络\n");
            ble_scan_wifi_networks();
            break;
        case CMD_CONNECT_WIFI:
            printf("🔗 处理命令: 连接WiFi\n");
            ble_connect_to_wifi();
            break;
        case CMD_RESTART:
            // 发送重启确认响应
            ble_send_response(RESP_ACK, "设备即将重启");
            printf("📱 收到重启命令，设备即将重启...\n");

            // 延迟重启，确保响应先发出
            vTaskDelay(500 / portTICK_PERIOD_MS);
            esp_restart();
            break;
        default:
            ble_send_error_response("未知命令");
            break;
    }
}

/**
 * @brief 处理数据 (与Arduino版本保持一致)
 */
void ble_handle_data(uint8_t sub_type, const uint8_t* data, size_t length) {
    printf("📊 处理数据消息:\n");
    printf("   子类型: 0x%02X (%s)\n", sub_type,
           sub_type == DATA_SSID ? "SSID" :
           sub_type == DATA_PASSWORD ? "密码" :
           sub_type == DATA_CONFIG ? "配置" : "未知");
    printf("   数据长度: %d\n", length);

    switch (sub_type) {
        case DATA_SSID:
            printf("📡 处理SSID数据:\n");
            printf("   最大SSID长度: %d\n", sizeof(ble_config_ctx.wifi_config.ssid));
            if (length < sizeof(ble_config_ctx.wifi_config.ssid)) {
                memcpy(ble_config_ctx.wifi_config.ssid, data, length);
                ble_config_ctx.wifi_config.ssid[length] = '\0';
                ble_send_response(RESP_ACK, "SSID设置成功");
                printf("✅ SSID设置成功: %s (长度: %d)\n", ble_config_ctx.wifi_config.ssid, strlen(ble_config_ctx.wifi_config.ssid));
            } else {
                printf("❌ SSID长度超限: %d > %d\n", length, sizeof(ble_config_ctx.wifi_config.ssid) - 1);
                ble_send_error_response("SSID长度超限");
            }
            break;
        case DATA_PASSWORD:
            printf("🔐 处理密码数据:\n");
            printf("   最大密码长度: %d\n", sizeof(ble_config_ctx.wifi_config.password));
            if (length < sizeof(ble_config_ctx.wifi_config.password)) {
                memcpy(ble_config_ctx.wifi_config.password, data, length);
                ble_config_ctx.wifi_config.password[length] = '\0';
                ble_config_ctx.wifi_config.valid = true;
                ble_send_response(RESP_ACK, "密码设置成功");
                printf("✅ 密码设置成功: *** (长度: %d)\n", strlen(ble_config_ctx.wifi_config.password));
                printf("🔧 WiFi配置标记为有效\n");
            } else {
                printf("❌ 密码长度超限: %d > %d\n", length, sizeof(ble_config_ctx.wifi_config.password) - 1);
                ble_send_error_response("密码长度超限");
            }
            break;
        default:
            printf("❌ 未知数据子类型: 0x%02X\n", sub_type);
            ble_send_error_response("未知数据类型");
            break;
    }
}

/**
 * @brief 发送设备信息 (与Arduino版本保持一致)
 */
void ble_send_device_info(void) {
    // 获取设备信息
    uint8_t mac[6];
    esp_read_mac(mac, ESP_MAC_WIFI_STA);

    // 构建MAC地址字符串
    char mac_str[18];
    snprintf(mac_str, sizeof(mac_str), "%02X:%02X:%02X:%02X:%02X:%02X",
             mac[0], mac[1], mac[2], mac[3], mac[4], mac[5]);

    // 构建JSON响应
    char device_info[128];
    snprintf(device_info, sizeof(device_info), "{\"mac\":\"%s\",\"sn\":\"%s\"}",
             mac_str, macStr);

    // 发送响应
    ble_send_packet(MSG_RESPONSE, RESP_DEVICE_INFO, (uint8_t*)device_info, strlen(device_info), false);
}

/**
 * @brief 扫描WiFi网络 (与Arduino版本保持一致)
 */
void ble_scan_wifi_networks(void) {
    esp_wifi_disconnect();

    wifi_scan_config_t scan_config = {
        .ssid = NULL,
        .bssid = NULL,
        .channel = 0,
        .show_hidden = false,
        .scan_type = WIFI_SCAN_TYPE_ACTIVE,
        .scan_time.active.min = 100,
        .scan_time.active.max = 300,
    };

    esp_wifi_scan_start(&scan_config, true);

    uint16_t ap_count = 0;
    esp_wifi_scan_get_ap_num(&ap_count);

    if (ap_count > 0) {
        wifi_ap_record_t *ap_list = malloc(sizeof(wifi_ap_record_t) * ap_count);
        esp_wifi_scan_get_ap_records(&ap_count, ap_list);

        // 构建WiFi列表JSON
        char wifi_list[1024] = "[";
        for (int i = 0; i < ap_count && i < 10; i++) { // 限制最多10个
            char entry[128];
            bool is_open = (ap_list[i].authmode == WIFI_AUTH_OPEN);
            snprintf(entry, sizeof(entry),
                    "{\"ssid\":\"%s\",\"rssi\":%d,\"secure\":%s}%s",
                    (char*)ap_list[i].ssid, ap_list[i].rssi,
                    is_open ? "false" : "true",
                    (i < ap_count - 1 && i < 9) ? "," : "");
            strcat(wifi_list, entry);
        }
        strcat(wifi_list, "]");

        // 发送响应
        ble_send_packet(MSG_RESPONSE, RESP_WIFI_LIST, (uint8_t*)wifi_list, strlen(wifi_list), false);

        free(ap_list);
    } else {
        ble_send_response(RESP_WIFI_LIST, "[]");
    }
}

/**
 * @brief 连接到WiFi (与Arduino版本保持一致)
 */
void ble_connect_to_wifi(void) {
    if (!ble_config_ctx.wifi_config.valid || strlen(ble_config_ctx.wifi_config.ssid) == 0) {
        ble_send_error_response("SSID未设置");
        return;
    }

    printf("🔄 连接到WiFi: %s\n", ble_config_ctx.wifi_config.ssid);

    // 保存配置到NVS
    set_wifi_config(ble_config_ctx.wifi_config.ssid, ble_config_ctx.wifi_config.password);

    // 发送成功响应 (简化版本，实际连接由主程序处理)
    char response[128];
    snprintf(response, sizeof(response), "{\"status\":\"success\",\"message\":\"配置已保存\"}");

    ble_send_packet(MSG_RESPONSE, RESP_WIFI_STATUS, (uint8_t*)response, strlen(response), false);

    // 设置配网成功状态
    ble_config_set_success(true);
    printf("🎉 配网完成!\n");
}

/**
 * @brief 发送响应
 */
void ble_send_response(uint8_t sub_type, const char* message) {
    ble_send_packet(MSG_RESPONSE, sub_type, (uint8_t*)message, strlen(message), false);
}

/**
 * @brief 发送错误响应
 */
void ble_send_error_response(const char* error_message) {
    ble_send_response(RESP_ERROR, error_message);
}

/**
 * @brief 初始化BLE WiFi配网模块
 */
esp_err_t ble_wifi_config_init(const char *device_name) {
#if CONFIG_BT_ENABLED
    esp_err_t ret;

    // 强制重置所有BLE配网状态
    ble_config_active = false;
    ble_config_success = false;
    ble_config_start_time = 0;

    // 初始化上下文
    memset(&ble_config_ctx, 0, sizeof(ble_config_ctx));
    ble_config_ctx.state = BLE_CONFIG_STATE_IDLE;

    printf("� BLE配网初始化，将使用sn作为设备名称: %s\n", sn);

    // 初始化蓝牙控制器
    esp_bt_controller_config_t bt_cfg = BT_CONTROLLER_INIT_CONFIG_DEFAULT();
    ret = esp_bt_controller_init(&bt_cfg);
    if (ret != ESP_OK) {
        ESP_LOGE(TAG, "Bluetooth controller init failed: %s", esp_err_to_name(ret));
        return ret;
    }

    // 启用蓝牙控制器
    ret = esp_bt_controller_enable(ESP_BT_MODE_BLE);
    if (ret != ESP_OK) {
        ESP_LOGE(TAG, "Bluetooth controller enable failed: %s", esp_err_to_name(ret));
        return ret;
    }

    // 初始化蓝牙栈
    ret = esp_bluedroid_init();
    if (ret != ESP_OK) {
        ESP_LOGE(TAG, "Bluetooth stack init failed: %s", esp_err_to_name(ret));
        return ret;
    }

    // 启用蓝牙栈
    ret = esp_bluedroid_enable();
    if (ret != ESP_OK) {
        ESP_LOGE(TAG, "Bluetooth stack enable failed: %s", esp_err_to_name(ret));
        return ret;
    }

    // 注册GAP回调
    ret = esp_ble_gap_register_callback(ble_gap_event_handler);
    if (ret != ESP_OK) {
        ESP_LOGE(TAG, "GAP register callback failed: %s", esp_err_to_name(ret));
        return ret;
    }

    // 注册GATTS回调
    ret = esp_ble_gatts_register_callback(ble_gatts_event_handler);
    if (ret != ESP_OK) {
        ESP_LOGE(TAG, "GATTS register callback failed: %s", esp_err_to_name(ret));
        return ret;
    }

    // 注册GATTS应用
    ret = esp_ble_gatts_app_register(0);
    if (ret != ESP_OK) {
        ESP_LOGE(TAG, "GATTS app register failed: %s", esp_err_to_name(ret));
        return ret;
    }

    // 创建超时定时器
    timeout_timer = xTimerCreate("ble_config_timeout",
                                pdMS_TO_TICKS(BLE_CONFIG_TIMEOUT_MS),
                                pdFALSE, NULL, ble_config_timeout_callback);
    if (timeout_timer == NULL) {
        ESP_LOGE(TAG, "Failed to create timeout timer");
        return ESP_ERR_NO_MEM;
    }

    ESP_LOGI(TAG, "BLE WiFi config module initialized");
    printf("🔵 BLE WiFi配网模块初始化完成\n");
    return ESP_OK;
#else
    ESP_LOGW(TAG, "Bluetooth not enabled in configuration");
    printf("❌ 蓝牙未启用，无法初始化BLE配网\n");
    return ESP_ERR_NOT_SUPPORTED;
#endif
}

/**
 * @brief 启动BLE WiFi配网
 */
esp_err_t ble_wifi_config_start(void) {
#if CONFIG_BT_ENABLED
    // 强制重置状态，防止之前的状态残留
    if (ble_config_active) {
        ESP_LOGW(TAG, "BLE config was active, forcing reset...");
        ble_wifi_config_stop();
        vTaskDelay(100 / portTICK_PERIOD_MS); // 短暂延迟确保停止完成
    }

    ESP_LOGI(TAG, "Starting BLE WiFi configuration...");

    // 重置状态
    ble_config_ctx.state = BLE_CONFIG_STATE_ADVERTISING;
    ble_config_ctx.config_success = false;
    ble_config_ctx.device_connected = false;
    memset(&ble_config_ctx.wifi_config, 0, sizeof(ble_config_ctx.wifi_config));

    // 记录开始时间
    ble_config_start_time = xTaskGetTickCount() * portTICK_PERIOD_MS;
    ble_config_active = true;
    ble_config_success = false;

    // 启动超时定时器
    if (timeout_timer != NULL) {
        xTimerStart(timeout_timer, 0);
    }

    // 进入蓝牙配网模式，LED快闪
    ESP_LOGI(TAG, "💡 进入蓝牙配网模式，LED快闪");
    led_set_fast_blink();

    printf("========================================\n");
    printf("🔵 蓝牙配网已启动\n");
    printf("📱 请使用手机APP进行WiFi配网\n");
    printf("⏰ 配网将在5分钟后自动超时\n");
    printf("⏱️  开始时间: %lu ms\n", ble_config_start_time);
    printf("📡 设备名称: %s\n", sn);
    printf("📍 MAC地址: %s\n", macStr);
    printf("💡 LED快闪指示配网模式\n");
    printf("========================================\n");

    return ESP_OK;
#else
    ESP_LOGW(TAG, "Bluetooth not enabled, cannot start BLE config");
    printf("❌ 蓝牙未启用，无法启动蓝牙配网\n");
    return ESP_ERR_NOT_SUPPORTED;
#endif
}

/**
 * @brief 停止BLE WiFi配网
 */
esp_err_t ble_wifi_config_stop(void) {
#if CONFIG_BT_ENABLED
    if (!ble_config_active) {
        return ESP_OK;
    }

    ESP_LOGI(TAG, "Stopping BLE WiFi configuration...");

    // 停止广播
    esp_ble_gap_stop_advertising();

    // 断开连接
    if (ble_config_ctx.device_connected) {
        esp_ble_gatts_close(ble_config_ctx.gatts_if, ble_config_ctx.conn_id);
    }

    // 停止超时定时器
    if (timeout_timer != NULL) {
        xTimerStop(timeout_timer, 0);
    }

    ble_config_active = false;

    // 蓝牙配网停止，LED熄灭
    ESP_LOGI(TAG, "💡 蓝牙配网停止，LED熄灭");
    led_set_off();

    printf("🔴 蓝牙配网已停止\n");

    return ESP_OK;
#else
    return ESP_ERR_NOT_SUPPORTED;
#endif
}

/**
 * @brief 反初始化BLE WiFi配网模块
 */
esp_err_t ble_wifi_config_deinit(void) {
#if CONFIG_BT_ENABLED
    // 停止配网
    ble_wifi_config_stop();

    // 删除定时器
    if (timeout_timer != NULL) {
        xTimerDelete(timeout_timer, 0);
        timeout_timer = NULL;
    }

    // 禁用蓝牙栈
    esp_bluedroid_disable();
    esp_bluedroid_deinit();

    // 禁用蓝牙控制器
    esp_bt_controller_disable();
    esp_bt_controller_deinit();

    ESP_LOGI(TAG, "BLE WiFi config module deinitialized");
    printf("🔴 BLE WiFi配网模块已清理\n");
    return ESP_OK;
#else
    return ESP_ERR_NOT_SUPPORTED;
#endif
}

// 其他辅助函数实现
void ble_wifi_config_update(void) {
    // 处理定期任务
    static uint32_t last_status_print = 0;
    uint32_t current_time = xTaskGetTickCount() * portTICK_PERIOD_MS;

    // 每10秒打印一次状态
    if (ble_config_active && (current_time - last_status_print) > 10000) {
        printf("\n📊 BLE配网状态检查:\n");
        printf("   配网活跃: %s\n", ble_config_active ? "是" : "否");
        printf("   设备连接: %s\n", ble_config_ctx.device_connected ? "是" : "否");
        printf("   连接ID: %d\n", ble_config_ctx.conn_id);
        printf("   GATTS接口: %d\n", ble_config_ctx.gatts_if);
        printf("   命令特征句柄: %d\n", ble_config_ctx.cmd_char_handle);
        printf("   数据特征句柄: %d\n", ble_config_ctx.data_char_handle);
        printf("   配网状态: %d\n", ble_config_ctx.state);
        printf("   运行时间: %lu ms\n", current_time - ble_config_start_time);

        last_status_print = current_time;
    }
}

bool ble_config_is_timeout(void) {
    if (!ble_config_active) return false;
    uint32_t current_time = xTaskGetTickCount() * portTICK_PERIOD_MS;
    return (current_time - ble_config_start_time) > BLE_CONFIG_TIMEOUT_MS;
}

ble_config_state_t ble_config_get_state(void) {
    return ble_config_ctx.state;
}

void ble_config_set_success(bool success) {
    ble_config_ctx.config_success = success;
    ble_config_success = success;
    if (success) {
        ble_config_ctx.state = BLE_CONFIG_STATE_SUCCESS;

        // 蓝牙配网成功，LED熄灭
        ESP_LOGI(TAG, "💡 蓝牙配网成功，LED熄灭");
        led_set_off();
    }
}

bool ble_config_is_success(void) {
    return ble_config_ctx.config_success;
}

ble_wifi_config_data_t* ble_config_get_wifi_data(void) {
    return &ble_config_ctx.wifi_config;
}

void ble_config_print_status(void) {
    printf("\n=== BLE Config Status ===\n");
    printf("Active: %s\n", ble_config_active ? "Yes" : "No");
    printf("Connected: %s\n", ble_config_ctx.device_connected ? "Yes" : "No");
    printf("Success: %s\n", ble_config_ctx.config_success ? "Yes" : "No");
    printf("State: %d\n", ble_config_ctx.state);
    if (ble_config_ctx.wifi_config.valid) {
        printf("SSID: %s\n", ble_config_ctx.wifi_config.ssid);
        printf("Password: %s\n", strlen(ble_config_ctx.wifi_config.password) > 0 ? "***" : "(empty)");
    }
    printf("========================\n");
}


